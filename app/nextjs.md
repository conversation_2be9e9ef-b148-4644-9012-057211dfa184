# Next.js & Supabase 專案入門教學

歡迎來到您的 Next.js 專案！這份文件是根據您專案的 `README.md` 和 `package.json` 客製化的教學，旨在幫助初學者快速上手 Next.js 和 Supabase。

## 1. 專案介紹

這個專案是使用 **Next.js** 框架建立的。Next.js 是一個基於 **React** 的全端開發框架，讓您可以用 React 來打造前端頁面，同時也內建了後端伺服器功能，可以處理 API 請求、資料庫互動等。

除了 Next.js，這個專案還整合了 **Supabase** 作為後端即服務 (Backend as a Service, BaaS)。

-   **Next.js**：負責處理網站的頁面渲染、路由、前端互動邏輯。
-   **Supabase**：提供了一個 PostgreSQL 資料庫、使用者身份驗證 (Authentication)、以及檔案儲存等服務。您可以把它想像成一個開源版的 Firebase。

從 `package.json` 和檔案結構來看，這是一個「Meme 產生器 (Meme Creator)」應用，核心功能是讓使用者在畫布 (Canvas) 上進行圖文編輯。

## 2. 環境設定與啟動

根據 `README.md` 和 `package.json`，請依照以下步驟啟動專案：

**第一步：安裝依賴套件**
如果您是第一次開啟這個專案，請先在終端機中執行以下指令來安裝所有需要的函式庫：
```bash
npm install
```

**第二步：啟動本地開發伺服器**
安裝完成後，執行以下指令：
```bash
npm run dev
```
這個指令會啟動一個本地的開發伺服器。`--turbopack` 是一個由 Next.js 官方開發的 Rust 製打包工具，能大幅提升啟動和更新的速度。

**第三步：開啟瀏覽器**
伺服器啟動後，您可以在瀏覽器中開啟 [http://localhost:3000](http://localhost:3000) 來查看應用程式。當您修改程式碼時 (例如 `src/app/page.tsx`)，頁面會自動更新，無需手動刷新。

## 3. 核心技術 - Next.js

### App Router (應用程式路由)

這個專案使用了 Next.js 最新的 **App Router** 機制，它的核心概念是「基於檔案系統的路由」。

-   **`src/app/`**：是您應用程式的主要目錄。
-   **資料夾定義路由**：`app` 目錄下的每個資料夾都對應一個 URL 路徑。例如 `app/dashboard/` 會對應到 `http://localhost:3000/dashboard`。
-   **`page.tsx`**：是構成該路徑 UI 的檔案。例如 `src/app/page.tsx` 就是首頁 (`/`) 的內容。
-   **`layout.tsx`**：定義了共享的 UI 佈局。例如 `src/app/layout.tsx` 定義了所有頁面都會共用的最外層 HTML 結構，您可以在這裡放置 header, footer 等。

### 元件 (Components)

在 Next.js App Router 中，元件分為兩種：

1.  **伺服器元件 (Server Components)**：
    -   這是 **預設** 的元件類型。
    -   它們在伺服器端渲染，可以非同步地直接存取後端資源 (例如直接查詢資料庫或讀取檔案)，但不能使用互動性的 React Hooks (如 `useState`, `useEffect`)。
    -   適合用來獲取資料並展示。

2.  **客戶端元件 (Client Components)**：
    -   若元件需要互動性 (例如點擊按鈕、處理表單輸入)，您必須在檔案頂部加上 `"use client";` 指示詞。
    -   它們會在瀏覽器中渲染和執行，就像傳統的 React 元件一樣。
    -   專案中的 `src/components/editor/Canvas.tsx` 這種需要大量使用者互動的畫布元件，必定是客戶端元件。

### 樣式 (Styling)

專案使用 **Tailwind CSS** 來處理樣式。這是一個 "utility-first" 的 CSS 框架，讓您直接在 JSX/TSX 中透過 class name 來組合出樣式，而不是撰寫獨立的 CSS 檔案。

-   **`src/app/globals.css`**：存放全域的 CSS 樣式和 Tailwind 的基礎設定。
-   **`tailwind-merge` & `clsx`**：這兩個在 `package.json` 中的工具，通常用來輔助 Tailwind，讓您可以更有條件地、更清晰地組合 class name。

## 4. 核心技術 - Supabase

### 什麼是 Supabase？

Supabase 是一個提供完整後端功能的平台。在這個專案中，它主要用於：
-   **資料庫**：提供一個功能齊全的 PostgreSQL 資料庫。
-   **身份驗證**：處理使用者註冊、登入、登出等。
-   **Row Level Security (RLS)**：一種精細的資料庫權限控制，可以設定「使用者只能讀取或修改自己的資料」等規則。

### 資料庫結構

專案根目錄下的 **`supabase/`** 資料夾非常重要：

-   **`schema.sql`**：這個檔案定義了資料庫的「結構」，例如有哪些資料表 (tables)、每個表有哪些欄位 (columns) 和它們的資料類型。
-   **`rls-policies.sql`**：定義了資料庫的「權限規則」。這是 Supabase 的一大特色，能確保資料的安全性。

### 整合方式

-   **`src/lib/supabase.ts`**：這個檔案通常是設定和初始化 Supabase 客戶端的地方，讓您的 Next.js 應用程式可以和 Supabase 專案進行通訊。
-   **`@supabase/ssr`**：這個套件專門用來在 Next.js 的伺服器環境和客戶端環境之間安全地管理使用者的登入狀態 (session)。
-   **`src/components/auth/`**：這個目錄下的元件 (如 `LoginForm.tsx`) 處理所有與使用者登入、註冊相關的 UI 和邏輯。

## 5. 重要依賴套件

-   **`@radix-ui/*`**：一系列高品質、無樣式、功能完整的「無頭 (headless)」UI 元件，例如對話框 (`Dialog`)、下拉選單 (`Dropdown-menu`) 等。它們提供了複雜的互動行為和無障礙性 (accessibility)，讓您可以專注在樣式上。
-   **`lucide-react`**：一個簡潔、漂亮的圖示 (icon) 庫。
-   **`fabric` / `konva` / `react-konva`**：這些是強大的 JavaScript 畫布 (Canvas) 函式庫，是這個「Meme 產生器」的核心。它們讓您可以在網頁上建立複雜的圖形、圖片和文字編輯功能。

## 6. 下一步

對於初學者，建議的探索路徑如下：

1.  **從 UI 開始**：閱讀 `src/app/page.tsx` 和 `src/app/layout.tsx`，了解頁面是如何被組裝起來的。
2.  **探索核心功能**：深入 `src/components/editor/Editor.tsx` 和 `src/components/editor/Canvas.tsx`，了解畫布編輯器是如何運作的。
3.  **了解資料流**：查看 `src/lib/supabase.ts` 和 `src/lib/database.ts` (如果有的話)，並追蹤一個功能 (例如「儲存模板」) 是如何呼叫 Supabase 來存取資料的。
4.  **查看後端定義**：打開 `supabase/schema.sql` 來了解資料庫中有哪些表。

### 學習資源

-   [Next.js 官方文件](https://nextjs.org/docs)
-   [Next.js 互動式教學](https://nextjs.org/learn)
-   [Supabase 官方文件](https://supabase.com/docs)

希望這份文件對您有幫助！
