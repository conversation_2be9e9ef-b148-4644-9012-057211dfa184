(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2099:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});var a=r(3865);let i=()=>(0,a.createBrowserClient)("http://localhost:54321","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQw59PobUkCALLBACK_NOT_USED")},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4866:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4147,23)),Promise.resolve().then(r.t.bind(r,8489,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7606))},7606:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n,A:()=>o});var a=r(2115),i=r(2099);let l={async getProfile(e){let t=(0,i.i)(),{data:r,error:a}=await t.from("profiles").select("*").eq("id",e).single();if(a)throw a;return r},async updateProfile(e,t){let r=(0,i.i)(),{data:a,error:l}=await r.from("profiles").update(t).eq("id",e).select().single();if(l)throw l;return a},async getUserTemplates(e){let t=(0,i.i)(),{data:r,error:a}=await t.from("templates").select("*").eq("author_id",e).order("created_at",{ascending:!1});if(a)throw a;return r},async getUserFavorites(e){let t=(0,i.i)(),{data:r,error:a}=await t.from("user_favorites").select("\n        *,\n        templates (\n          *,\n          profiles:author_id (\n            id,\n            username,\n            full_name,\n            avatar_url\n          )\n        )\n      ").eq("user_id",e).order("created_at",{ascending:!1});if(a)throw a;return r}},s=(0,a.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,a.useState)(null),[o,u]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),f=(0,i.i)();(0,a.useEffect)(()=>{(async()=>{var e;let{data:{session:t}}=await f.auth.getSession();if(n(null!=(e=null==t?void 0:t.user)?e:null),null==t?void 0:t.user)try{let e=await l.getProfile(t.user.id);u(e)}catch(e){console.error("Error fetching user profile:",e)}d(!1)})();let{data:{subscription:e}}=f.auth.onAuthStateChange(async(e,t)=>{var r;if(n(null!=(r=null==t?void 0:t.user)?r:null),null==t?void 0:t.user)try{let e=await l.getProfile(t.user.id);u(e)}catch(e){console.error("Error fetching user profile:",e),u(null)}else u(null);d(!1)});return()=>e.unsubscribe()},[f.auth]);let h=async(e,t)=>{let{error:r}=await f.auth.signInWithPassword({email:e,password:t});if(r)throw r},_=async(e,t,r)=>{let{error:a}=await f.auth.signUp({email:e,password:t,options:{data:r}});if(a)throw a},w=async()=>{let{error:e}=await f.auth.signOut();if(e)throw e},m=async e=>{if(!r)throw Error("No user logged in");u(await l.updateProfile(r.id,e))};return(0,a.createElement)(s.Provider,{value:{user:r,profile:o,loading:c,signIn:h,signUp:_,signOut:w,updateProfile:m}},t)}function o(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{e.O(0,[896,865,441,964,358],()=>e(e.s=4866)),_N_E=e.O()}]);