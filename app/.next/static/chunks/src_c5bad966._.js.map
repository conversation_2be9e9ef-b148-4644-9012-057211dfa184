{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function downloadImage(dataUrl: string, filename: string) {\n  const link = document.createElement('a')\n  link.download = filename\n  link.href = dataUrl\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n}\n\nexport function resizeImage(file: File, maxWidth: number, maxHeight: number): Promise<string> {\n  return new Promise((resolve) => {\n    const canvas = document.createElement('canvas')\n    const ctx = canvas.getContext('2d')!\n    const img = new Image()\n    \n    img.onload = () => {\n      const { width, height } = img\n      const ratio = Math.min(maxWidth / width, maxHeight / height)\n      \n      canvas.width = width * ratio\n      canvas.height = height * ratio\n      \n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)\n      resolve(canvas.toDataURL('image/png'))\n    }\n    \n    img.src = URL.createObjectURL(file)\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,OAAe,EAAE,QAAgB;IAC7D,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,QAAQ,GAAG;IAChB,KAAK,IAAI,GAAG;IACZ,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAEO,SAAS,YAAY,IAAU,EAAE,QAAgB,EAAE,SAAiB;IACzE,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,MAAM,MAAM,IAAI;QAEhB,IAAI,MAAM,GAAG;YACX,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YAC1B,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,OAAO,YAAY;YAErD,OAAO,KAAK,GAAG,QAAQ;YACvB,OAAO,MAAM,GAAG,SAAS;YAEzB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACpD,QAAQ,OAAO,SAAS,CAAC;QAC3B;QAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useEditor.ts"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useReducer, useCallback, createElement } from 'react'\nimport { EditorState, EditorTool, CanvasData, ObjectLayer, TextLayer } from '@/types'\nimport { generateId } from '@/lib/utils'\n\ninterface EditorContextType extends EditorState {\n  setTool: (tool: EditorTool) => void\n  selectObject: (id: string | null) => void\n  addTextLayer: (text: string, x: number, y: number) => void\n  addObjectLayer: (src: string, x: number, y: number) => void\n  updateTextLayer: (id: string, updates: Partial<TextLayer>) => void\n  updateObjectLayer: (id: string, updates: Partial<ObjectLayer>) => void\n  deleteLayer: (id: string) => void\n  setBackground: (background: CanvasData['background']) => void\n  moveLayer: (id: string, x: number, y: number) => void\n  resizeLayer: (id: string, width: number, height: number) => void\n  rotateLayer: (id: string, rotation: number) => void\n  setLayerOpacity: (id: string, opacity: number) => void\n  bringToFront: (id: string) => void\n  sendToBack: (id: string) => void\n  undo: () => void\n  redo: () => void\n  clearCanvas: () => void\n  loadCanvasData: (data: CanvasData) => void\n}\n\ntype EditorAction =\n  | { type: 'SET_TOOL'; payload: EditorTool }\n  | { type: 'SELECT_OBJECT'; payload: string | null }\n  | { type: 'ADD_TEXT_LAYER'; payload: { text: string; x: number; y: number } }\n  | { type: 'ADD_OBJECT_LAYER'; payload: { src: string; x: number; y: number } }\n  | { type: 'UPDATE_TEXT_LAYER'; payload: { id: string; updates: Partial<TextLayer> } }\n  | { type: 'UPDATE_OBJECT_LAYER'; payload: { id: string; updates: Partial<ObjectLayer> } }\n  | { type: 'DELETE_LAYER'; payload: string }\n  | { type: 'SET_BACKGROUND'; payload: CanvasData['background'] }\n  | { type: 'MOVE_LAYER'; payload: { id: string; x: number; y: number } }\n  | { type: 'RESIZE_LAYER'; payload: { id: string; width: number; height: number } }\n  | { type: 'ROTATE_LAYER'; payload: { id: string; rotation: number } }\n  | { type: 'SET_LAYER_OPACITY'; payload: { id: string; opacity: number } }\n  | { type: 'BRING_TO_FRONT'; payload: string }\n  | { type: 'SEND_TO_BACK'; payload: string }\n  | { type: 'UNDO' }\n  | { type: 'REDO' }\n  | { type: 'CLEAR_CANVAS' }\n  | { type: 'LOAD_CANVAS_DATA'; payload: CanvasData }\n  | { type: 'SET_LOADING'; payload: boolean }\n\nconst initialCanvasData: CanvasData = {\n  width: 800,\n  height: 600,\n  background: {\n    type: 'gradient',\n    value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n  },\n  objects: [],\n  texts: [\n    {\n      id: 'default-text-1',\n      type: 'text',\n      content: '早上一聲好',\n      x: 350,\n      y: 100,\n      fontSize: 48,\n      fontFamily: 'Microsoft JhengHei',\n      fontWeight: 'bold',\n      color: '#FFD700',\n      textAlign: 'center',\n      rotation: 0,\n      opacity: 1,\n      zIndex: 2,\n      stroke: '#000000',\n      strokeWidth: 2\n    },\n    {\n      id: 'default-text-2',\n      type: 'text',\n      content: '事事皆美好',\n      x: 350,\n      y: 180,\n      fontSize: 48,\n      fontFamily: 'Microsoft JhengHei',\n      fontWeight: 'bold',\n      color: '#FFD700',\n      textAlign: 'center',\n      rotation: 0,\n      opacity: 1,\n      zIndex: 3,\n      stroke: '#000000',\n      strokeWidth: 2\n    },\n    {\n      id: 'default-text-3',\n      type: 'text',\n      content: '祝您有美好的一天！',\n      x: 400,\n      y: 450,\n      fontSize: 24,\n      fontFamily: 'Microsoft JhengHei',\n      fontWeight: 'normal',\n      color: '#FFFFFF',\n      textAlign: 'center',\n      rotation: 0,\n      opacity: 1,\n      zIndex: 4\n    }\n  ]\n}\n\nconst initialState: EditorState = {\n  tool: 'text',\n  selectedObjectId: null,\n  canvasData: initialCanvasData,\n  history: [initialCanvasData],\n  historyIndex: 0,\n  isLoading: false\n}\n\nfunction editorReducer(state: EditorState, action: EditorAction): EditorState {\n  const saveToHistory = (newCanvasData: CanvasData) => {\n    const newHistory = state.history.slice(0, state.historyIndex + 1)\n    newHistory.push(newCanvasData)\n    return {\n      history: newHistory.slice(-20), // Keep only last 20 states\n      historyIndex: Math.min(newHistory.length - 1, 19)\n    }\n  }\n\n  switch (action.type) {\n    case 'SET_TOOL':\n      return { ...state, tool: action.payload }\n\n    case 'SELECT_OBJECT':\n      return { ...state, selectedObjectId: action.payload }\n\n    case 'ADD_TEXT_LAYER': {\n      const newText: TextLayer = {\n        id: generateId(),\n        type: 'text',\n        content: action.payload.text,\n        x: action.payload.x,\n        y: action.payload.y,\n        fontSize: 24,\n        fontFamily: 'Arial',\n        fontWeight: 'normal',\n        color: '#000000',\n        textAlign: 'left',\n        rotation: 0,\n        opacity: 1,\n        zIndex: Math.max(...state.canvasData.texts.map(t => t.zIndex), 0) + 1\n      }\n      const newCanvasData = {\n        ...state.canvasData,\n        texts: [...state.canvasData.texts, newText]\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        selectedObjectId: newText.id,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'ADD_OBJECT_LAYER': {\n      const newObject: ObjectLayer = {\n        id: generateId(),\n        type: 'image',\n        src: action.payload.src,\n        x: action.payload.x,\n        y: action.payload.y,\n        width: 200,\n        height: 200,\n        rotation: 0,\n        opacity: 1,\n        flipX: false,\n        flipY: false,\n        zIndex: Math.max(...state.canvasData.objects.map(o => o.zIndex), 0) + 1\n      }\n      const newCanvasData = {\n        ...state.canvasData,\n        objects: [...state.canvasData.objects, newObject]\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        selectedObjectId: newObject.id,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'UPDATE_TEXT_LAYER': {\n      const newCanvasData = {\n        ...state.canvasData,\n        texts: state.canvasData.texts.map(text =>\n          text.id === action.payload.id\n            ? { ...text, ...action.payload.updates }\n            : text\n        )\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'UPDATE_OBJECT_LAYER': {\n      const newCanvasData = {\n        ...state.canvasData,\n        objects: state.canvasData.objects.map(obj =>\n          obj.id === action.payload.id\n            ? { ...obj, ...action.payload.updates }\n            : obj\n        )\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'DELETE_LAYER': {\n      const newCanvasData = {\n        ...state.canvasData,\n        texts: state.canvasData.texts.filter(text => text.id !== action.payload),\n        objects: state.canvasData.objects.filter(obj => obj.id !== action.payload)\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        selectedObjectId: state.selectedObjectId === action.payload ? null : state.selectedObjectId,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'SET_BACKGROUND': {\n      const newCanvasData = {\n        ...state.canvasData,\n        background: action.payload\n      }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'UNDO':\n      if (state.historyIndex > 0) {\n        const newIndex = state.historyIndex - 1\n        return {\n          ...state,\n          canvasData: state.history[newIndex],\n          historyIndex: newIndex,\n          selectedObjectId: null\n        }\n      }\n      return state\n\n    case 'REDO':\n      if (state.historyIndex < state.history.length - 1) {\n        const newIndex = state.historyIndex + 1\n        return {\n          ...state,\n          canvasData: state.history[newIndex],\n          historyIndex: newIndex,\n          selectedObjectId: null\n        }\n      }\n      return state\n\n    case 'CLEAR_CANVAS': {\n      const newCanvasData = { ...initialCanvasData }\n      return {\n        ...state,\n        canvasData: newCanvasData,\n        selectedObjectId: null,\n        ...saveToHistory(newCanvasData)\n      }\n    }\n\n    case 'LOAD_CANVAS_DATA':\n      return {\n        ...state,\n        canvasData: action.payload,\n        selectedObjectId: null,\n        ...saveToHistory(action.payload)\n      }\n\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload }\n\n    default:\n      return state\n  }\n}\n\nconst EditorContext = createContext<EditorContextType | undefined>(undefined)\n\nexport function EditorProvider({ children }: { children: React.ReactNode }) {\n  const [state, dispatch] = useReducer(editorReducer, initialState)\n\n  const setTool = useCallback((tool: EditorTool) => {\n    dispatch({ type: 'SET_TOOL', payload: tool })\n  }, [])\n\n  const selectObject = useCallback((id: string | null) => {\n    dispatch({ type: 'SELECT_OBJECT', payload: id })\n  }, [])\n\n  const addTextLayer = useCallback((text: string, x: number, y: number) => {\n    dispatch({ type: 'ADD_TEXT_LAYER', payload: { text, x, y } })\n  }, [])\n\n  const addObjectLayer = useCallback((src: string, x: number, y: number) => {\n    dispatch({ type: 'ADD_OBJECT_LAYER', payload: { src, x, y } })\n  }, [])\n\n  const updateTextLayer = useCallback((id: string, updates: Partial<TextLayer>) => {\n    dispatch({ type: 'UPDATE_TEXT_LAYER', payload: { id, updates } })\n  }, [])\n\n  const updateObjectLayer = useCallback((id: string, updates: Partial<ObjectLayer>) => {\n    dispatch({ type: 'UPDATE_OBJECT_LAYER', payload: { id, updates } })\n  }, [])\n\n  const deleteLayer = useCallback((id: string) => {\n    dispatch({ type: 'DELETE_LAYER', payload: id })\n  }, [])\n\n  const setBackground = useCallback((background: CanvasData['background']) => {\n    dispatch({ type: 'SET_BACKGROUND', payload: background })\n  }, [])\n\n  const moveLayer = useCallback((id: string, x: number, y: number) => {\n    const isText = state.canvasData.texts.some(t => t.id === id)\n    if (isText) {\n      updateTextLayer(id, { x, y })\n    } else {\n      updateObjectLayer(id, { x, y })\n    }\n  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])\n\n  const resizeLayer = useCallback((id: string, width: number, height: number) => {\n    updateObjectLayer(id, { width, height })\n  }, [updateObjectLayer])\n\n  const rotateLayer = useCallback((id: string, rotation: number) => {\n    const isText = state.canvasData.texts.some(t => t.id === id)\n    if (isText) {\n      updateTextLayer(id, { rotation })\n    } else {\n      updateObjectLayer(id, { rotation })\n    }\n  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])\n\n  const setLayerOpacity = useCallback((id: string, opacity: number) => {\n    const isText = state.canvasData.texts.some(t => t.id === id)\n    if (isText) {\n      updateTextLayer(id, { opacity })\n    } else {\n      updateObjectLayer(id, { opacity })\n    }\n  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])\n\n  const bringToFront = useCallback((id: string) => {\n    const maxZ = Math.max(\n      ...state.canvasData.texts.map(t => t.zIndex),\n      ...state.canvasData.objects.map(o => o.zIndex)\n    )\n    const isText = state.canvasData.texts.some(t => t.id === id)\n    if (isText) {\n      updateTextLayer(id, { zIndex: maxZ + 1 })\n    } else {\n      updateObjectLayer(id, { zIndex: maxZ + 1 })\n    }\n  }, [state.canvasData, updateTextLayer, updateObjectLayer])\n\n  const sendToBack = useCallback((id: string) => {\n    const minZ = Math.min(\n      ...state.canvasData.texts.map(t => t.zIndex),\n      ...state.canvasData.objects.map(o => o.zIndex)\n    )\n    const isText = state.canvasData.texts.some(t => t.id === id)\n    if (isText) {\n      updateTextLayer(id, { zIndex: minZ - 1 })\n    } else {\n      updateObjectLayer(id, { zIndex: minZ - 1 })\n    }\n  }, [state.canvasData, updateTextLayer, updateObjectLayer])\n\n  const undo = useCallback(() => {\n    dispatch({ type: 'UNDO' })\n  }, [])\n\n  const redo = useCallback(() => {\n    dispatch({ type: 'REDO' })\n  }, [])\n\n  const clearCanvas = useCallback(() => {\n    dispatch({ type: 'CLEAR_CANVAS' })\n  }, [])\n\n  const loadCanvasData = useCallback((data: CanvasData) => {\n    dispatch({ type: 'LOAD_CANVAS_DATA', payload: data })\n  }, [])\n\n  const value = {\n    ...state,\n    setTool,\n    selectObject,\n    addTextLayer,\n    addObjectLayer,\n    updateTextLayer,\n    updateObjectLayer,\n    deleteLayer,\n    setBackground,\n    moveLayer,\n    resizeLayer,\n    rotateLayer,\n    setLayerOpacity,\n    bringToFront,\n    sendToBack,\n    undo,\n    redo,\n    clearCanvas,\n    loadCanvasData\n  }\n\n  return createElement(EditorContext.Provider, { value }, children)\n}\n\nexport function useEditor() {\n  const context = useContext(EditorContext)\n  if (context === undefined) {\n    throw new Error('useEditor must be used within an EditorProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;AAJA;;;AAgDA,MAAM,oBAAgC;IACpC,OAAO;IACP,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,SAAS,EAAE;IACX,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,GAAG;YACH,GAAG;YACH,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,GAAG;YACH,GAAG;YACH,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,GAAG;YACH,GAAG;YACH,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,QAAQ;QACV;KACD;AACH;AAEA,MAAM,eAA4B;IAChC,MAAM;IACN,kBAAkB;IAClB,YAAY;IACZ,SAAS;QAAC;KAAkB;IAC5B,cAAc;IACd,WAAW;AACb;AAEA,SAAS,cAAc,KAAkB,EAAE,MAAoB;IAC7D,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,YAAY,GAAG;QAC/D,WAAW,IAAI,CAAC;QAChB,OAAO;YACL,SAAS,WAAW,KAAK,CAAC,CAAC;YAC3B,cAAc,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,GAAG;QAChD;IACF;IAEA,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,MAAM,OAAO,OAAO;YAAC;QAE1C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,kBAAkB,OAAO,OAAO;YAAC;QAEtD,KAAK;YAAkB;gBACrB,MAAM,UAAqB;oBACzB,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,SAAS,OAAO,OAAO,CAAC,IAAI;oBAC5B,GAAG,OAAO,OAAO,CAAC,CAAC;oBACnB,GAAG,OAAO,OAAO,CAAC,CAAC;oBACnB,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,QAAQ,KAAK,GAAG,IAAI,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;gBACtE;gBACA,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,OAAO;2BAAI,MAAM,UAAU,CAAC,KAAK;wBAAE;qBAAQ;gBAC7C;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,kBAAkB,QAAQ,EAAE;oBAC5B,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,YAAyB;oBAC7B,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,KAAK,OAAO,OAAO,CAAC,GAAG;oBACvB,GAAG,OAAO,OAAO,CAAC,CAAC;oBACnB,GAAG,OAAO,OAAO,CAAC,CAAC;oBACnB,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,QAAQ,KAAK,GAAG,IAAI,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK;gBACxE;gBACA,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,SAAS;2BAAI,MAAM,UAAU,CAAC,OAAO;wBAAE;qBAAU;gBACnD;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,kBAAkB,UAAU,EAAE;oBAC9B,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YAAqB;gBACxB,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,OAAO,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAChC,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACzB;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO,OAAO,CAAC,OAAO;wBAAC,IACrC;gBAER;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YAAuB;gBAC1B,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,SAAS,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,MACpC,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACxB;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO,OAAO,CAAC,OAAO;wBAAC,IACpC;gBAER;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YAAgB;gBACnB,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,OAAO,MAAM,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;oBACvE,SAAS,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO;gBAC3E;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,kBAAkB,MAAM,gBAAgB,KAAK,OAAO,OAAO,GAAG,OAAO,MAAM,gBAAgB;oBAC3F,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YAAkB;gBACrB,MAAM,gBAAgB;oBACpB,GAAG,MAAM,UAAU;oBACnB,YAAY,OAAO,OAAO;gBAC5B;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YACH,IAAI,MAAM,YAAY,GAAG,GAAG;gBAC1B,MAAM,WAAW,MAAM,YAAY,GAAG;gBACtC,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY,MAAM,OAAO,CAAC,SAAS;oBACnC,cAAc;oBACd,kBAAkB;gBACpB;YACF;YACA,OAAO;QAET,KAAK;YACH,IAAI,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjD,MAAM,WAAW,MAAM,YAAY,GAAG;gBACtC,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY,MAAM,OAAO,CAAC,SAAS;oBACnC,cAAc;oBACd,kBAAkB;gBACpB;YACF;YACA,OAAO;QAET,KAAK;YAAgB;gBACnB,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;gBAAC;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,YAAY;oBACZ,kBAAkB;oBAClB,GAAG,cAAc,cAAc;gBACjC;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,OAAO,OAAO;gBAC1B,kBAAkB;gBAClB,GAAG,cAAc,OAAO,OAAO,CAAC;YAClC;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAE/C;YACE,OAAO;IACX;AACF;AAEA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,eAAe;IAEpD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAC3B,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAK;QAC7C;8CAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAChC,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAG;QAChD;mDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,MAAc,GAAW;YACzD,SAAS;gBAAE,MAAM;gBAAkB,SAAS;oBAAE;oBAAM;oBAAG;gBAAE;YAAE;QAC7D;mDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,KAAa,GAAW;YAC1D,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAK;oBAAG;gBAAE;YAAE;QAC9D;qDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,IAAY;YAC/C,SAAS;gBAAE,MAAM;gBAAqB,SAAS;oBAAE;oBAAI;gBAAQ;YAAE;QACjE;sDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,IAAY;YACjD,SAAS;gBAAE,MAAM;gBAAuB,SAAS;oBAAE;oBAAI;gBAAQ;YAAE;QACnE;wDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAC/B,SAAS;gBAAE,MAAM;gBAAgB,SAAS;YAAG;QAC/C;kDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAW;QACzD;oDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,IAAY,GAAW;YACpD,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI;gEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACzD,IAAI,QAAQ;gBACV,gBAAgB,IAAI;oBAAE;oBAAG;gBAAE;YAC7B,OAAO;gBACL,kBAAkB,IAAI;oBAAE;oBAAG;gBAAE;YAC/B;QACF;gDAAG;QAAC,MAAM,UAAU,CAAC,KAAK;QAAE;QAAiB;KAAkB;IAE/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,IAAY,OAAe;YAC1D,kBAAkB,IAAI;gBAAE;gBAAO;YAAO;QACxC;kDAAG;QAAC;KAAkB;IAEtB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,IAAY;YAC3C,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI;kEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACzD,IAAI,QAAQ;gBACV,gBAAgB,IAAI;oBAAE;gBAAS;YACjC,OAAO;gBACL,kBAAkB,IAAI;oBAAE;gBAAS;YACnC;QACF;kDAAG;QAAC,MAAM,UAAU,CAAC,KAAK;QAAE;QAAiB;KAAkB;IAE/D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,IAAY;YAC/C,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI;sEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACzD,IAAI,QAAQ;gBACV,gBAAgB,IAAI;oBAAE;gBAAQ;YAChC,OAAO;gBACL,kBAAkB,IAAI;oBAAE;gBAAQ;YAClC;QACF;sDAAG;QAAC,MAAM,UAAU,CAAC,KAAK;QAAE;QAAiB;KAAkB;IAE/D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAChC,MAAM,OAAO,KAAK,GAAG,IAChB,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG;iEAAC,CAAA,IAAK,EAAE,MAAM;oEACxC,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG;iEAAC,CAAA,IAAK,EAAE,MAAM;;YAE/C,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI;mEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACzD,IAAI,QAAQ;gBACV,gBAAgB,IAAI;oBAAE,QAAQ,OAAO;gBAAE;YACzC,OAAO;gBACL,kBAAkB,IAAI;oBAAE,QAAQ,OAAO;gBAAE;YAC3C;QACF;mDAAG;QAAC,MAAM,UAAU;QAAE;QAAiB;KAAkB;IAEzD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,MAAM,OAAO,KAAK,GAAG,IAChB,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG;+DAAC,CAAA,IAAK,EAAE,MAAM;kEACxC,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG;+DAAC,CAAA,IAAK,EAAE,MAAM;;YAE/C,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI;iEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACzD,IAAI,QAAQ;gBACV,gBAAgB,IAAI;oBAAE,QAAQ,OAAO;gBAAE;YACzC,OAAO;gBACL,kBAAkB,IAAI;oBAAE,QAAQ,OAAO;gBAAE;YAC3C;QACF;iDAAG;QAAC,MAAM,UAAU;QAAE;QAAiB;KAAkB;IAEzD,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACvB,SAAS;gBAAE,MAAM;YAAO;QAC1B;2CAAG,EAAE;IAEL,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACvB,SAAS;gBAAE,MAAM;YAAO;QAC1B;2CAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,SAAS;gBAAE,MAAM;YAAe;QAClC;kDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,SAAS;gBAAE,MAAM;gBAAoB,SAAS;YAAK;QACrD;qDAAG,EAAE;IAEL,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,cAAc,QAAQ,EAAE;QAAE;IAAM,GAAG;AAC1D;GAnIgB;KAAA;AAqIT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Canvas.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useRef, useEffect, useState } from 'react'\nimport { Stage, Layer, Rect, Image as KonvaImage, Text, Transformer } from 'react-konva'\nimport { useEditor } from '@/hooks/useEditor'\nimport { ObjectLayer, TextLayer } from '@/types'\nimport Konva from 'konva'\n\ninterface CanvasProps {\n  width?: number\n  height?: number\n}\n\nexport function Canvas({ width = 800, height = 600 }: CanvasProps) {\n  const {\n    canvasData,\n    selectedObjectId,\n    selectObject,\n    moveLayer,\n    resizeLayer,\n    rotateLayer,\n    tool\n  } = useEditor()\n  \n  const stageRef = useRef<Konva.Stage>(null)\n  const transformerRef = useRef<Konva.Transformer>(null)\n  const [images, setImages] = useState<{ [key: string]: HTMLImageElement }>({})\n\n  // Load images for objects and background\n  useEffect(() => {\n    const loadImages = async () => {\n      const imagePromises = []\n\n      // Load object images\n      canvasData.objects.forEach(obj => {\n        if (!images[obj.id]) {\n          imagePromises.push(\n            new Promise<{ id: string; image: HTMLImageElement }>((resolve, reject) => {\n              const img = new window.Image()\n              img.crossOrigin = 'anonymous'\n              img.onload = () => resolve({ id: obj.id, image: img })\n              img.onerror = reject\n              img.src = obj.src\n            })\n          )\n        }\n      })\n\n      // Load background image\n      if (canvasData.background?.type === 'image' && !images['background']) {\n        imagePromises.push(\n          new Promise<{ id: string; image: HTMLImageElement }>((resolve, reject) => {\n            const img = new window.Image()\n            img.crossOrigin = 'anonymous'\n            img.onload = () => resolve({ id: 'background', image: img })\n            img.onerror = reject\n            img.src = canvasData.background!.value\n          })\n        )\n      }\n\n      if (imagePromises.length > 0) {\n        try {\n          const loadedImages = await Promise.all(imagePromises)\n          const imageMap = loadedImages.reduce((acc, { id, image }) => {\n            acc[id] = image\n            return acc\n          }, {} as { [key: string]: HTMLImageElement })\n\n          setImages(prev => ({ ...prev, ...imageMap }))\n        } catch (error) {\n          console.error('Error loading images:', error)\n        }\n      }\n    }\n\n    loadImages()\n  }, [canvasData.objects, canvasData.background, images])\n\n  // Handle transformer\n  useEffect(() => {\n    if (transformerRef.current && selectedObjectId) {\n      const stage = stageRef.current\n      if (stage) {\n        const selectedNode = stage.findOne(`#${selectedObjectId}`)\n        if (selectedNode) {\n          transformerRef.current.nodes([selectedNode])\n          transformerRef.current.getLayer()?.batchDraw()\n        }\n      }\n    } else if (transformerRef.current) {\n      transformerRef.current.nodes([])\n      transformerRef.current.getLayer()?.batchDraw()\n    }\n  }, [selectedObjectId])\n\n  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {\n    if (e.target === e.target.getStage()) {\n      selectObject(null)\n      return\n    }\n\n    const clickedId = e.target.id()\n    if (clickedId && tool === 'select') {\n      selectObject(clickedId)\n    }\n  }\n\n  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>, id: string) => {\n    moveLayer(id, e.target.x(), e.target.y())\n  }\n\n  const handleTransformEnd = (e: Konva.KonvaEventObject<Event>, id: string) => {\n    const node = e.target\n    const scaleX = node.scaleX()\n    const scaleY = node.scaleY()\n    \n    // Reset scale and apply to width/height\n    node.scaleX(1)\n    node.scaleY(1)\n    \n    const newWidth = Math.max(5, node.width() * scaleX)\n    const newHeight = Math.max(5, node.height() * scaleY)\n    \n    resizeLayer(id, newWidth, newHeight)\n    rotateLayer(id, node.rotation())\n    moveLayer(id, node.x(), node.y())\n  }\n\n  const renderBackground = () => {\n    if (!canvasData.background) return null\n\n    if (canvasData.background.type === 'color') {\n      return (\n        <Rect\n          width={width}\n          height={height}\n          fill={canvasData.background.value}\n        />\n      )\n    }\n\n    if (canvasData.background.type === 'gradient') {\n      // Create a simple gradient effect using multiple rects\n      return (\n        <Rect\n          width={width}\n          height={height}\n          fillLinearGradientStartPoint={{ x: 0, y: 0 }}\n          fillLinearGradientEndPoint={{ x: width, y: height }}\n          fillLinearGradientColorStops={[0, '#667eea', 1, '#764ba2']}\n        />\n      )\n    }\n\n    if (canvasData.background.type === 'image' && images['background']) {\n      return (\n        <KonvaImage\n          image={images['background']}\n          width={width}\n          height={height}\n          x={0}\n          y={0}\n        />\n      )\n    }\n\n    return null\n  }\n\n  const renderObjects = () => {\n    return canvasData.objects\n      .sort((a, b) => a.zIndex - b.zIndex)\n      .map((obj: ObjectLayer) => {\n        const image = images[obj.id]\n        if (!image) return null\n\n        return (\n          <KonvaImage\n            key={obj.id}\n            id={obj.id}\n            image={image}\n            x={obj.x}\n            y={obj.y}\n            width={obj.width}\n            height={obj.height}\n            rotation={obj.rotation}\n            opacity={obj.opacity}\n            scaleX={obj.flipX ? -1 : 1}\n            scaleY={obj.flipY ? -1 : 1}\n            draggable={tool === 'select'}\n            onDragEnd={(e) => handleDragEnd(e, obj.id)}\n            onTransformEnd={(e) => handleTransformEnd(e, obj.id)}\n            onClick={() => tool === 'select' && selectObject(obj.id)}\n          />\n        )\n      })\n  }\n\n  const renderTexts = () => {\n    return canvasData.texts\n      .sort((a, b) => a.zIndex - b.zIndex)\n      .map((text: TextLayer) => (\n        <React.Fragment key={text.id}>\n          <Text\n            id={text.id}\n            text={text.content}\n            x={text.x}\n            y={text.y}\n            width={text.width}\n            height={text.height}\n            fontSize={text.fontSize}\n            fontFamily={text.fontFamily}\n            fontStyle={text.fontWeight}\n            fill={text.color}\n            align={text.textAlign}\n            rotation={text.rotation}\n            opacity={text.opacity}\n            stroke={text.stroke}\n            strokeWidth={text.strokeWidth}\n            shadowColor={text.shadow?.color}\n            shadowBlur={text.shadow?.blur}\n            shadowOffsetX={text.shadow?.offsetX}\n            shadowOffsetY={text.shadow?.offsetY}\n            draggable={tool === 'select'}\n            onDragEnd={(e) => handleDragEnd(e, text.id)}\n            onTransformEnd={(e) => handleTransformEnd(e, text.id)}\n            onClick={() => {\n              // 點擊文字時自動切換到選擇模式並選中文字\n              if (tool !== 'select') {\n                setTool('select')\n              }\n              selectObject(text.id)\n            }}\n          />\n          {/* 為選中的文字添加虛線框 */}\n          {selectedObjectId === text.id && (\n            <Rect\n              x={text.x - 5}\n              y={text.y - 5}\n              width={(text.width || text.content.length * text.fontSize * 0.6) + 10}\n              height={(text.height || text.fontSize * 1.2) + 10}\n              stroke=\"#007bff\"\n              strokeWidth={2}\n              dash={[5, 5]}\n              fill=\"transparent\"\n              listening={false}\n            />\n          )}\n        </React.Fragment>\n      ))\n  }\n\n  return (\n    <div className=\"border border-gray-300 bg-white\">\n      <Stage\n        ref={stageRef}\n        width={width}\n        height={height}\n        onClick={handleStageClick}\n        onTap={handleStageClick}\n      >\n        <Layer>\n          {renderBackground()}\n          {renderObjects()}\n          {renderTexts()}\n        </Layer>\n        <Layer>\n          <Transformer\n            ref={transformerRef}\n            boundBoxFunc={(oldBox, newBox) => {\n              // Limit resize\n              if (newBox.width < 5 || newBox.height < 5) {\n                return oldBox\n              }\n              return newBox\n            }}\n          />\n        </Layer>\n      </Stage>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAaO,SAAS,OAAO,KAA0C;QAA1C,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAe,GAA1C;;IACrB,MAAM,EACJ,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,SAAS,EACT,WAAW,EACX,WAAW,EACX,IAAI,EACL,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAEZ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IACrC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC,CAAC;IAE3E,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;+CAAa;wBAmBb;oBAlBJ,MAAM,gBAAgB,EAAE;oBAExB,qBAAqB;oBACrB,WAAW,OAAO,CAAC,OAAO;uDAAC,CAAA;4BACzB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE;gCACnB,cAAc,IAAI,CAChB,IAAI;mEAAiD,CAAC,SAAS;wCAC7D,MAAM,MAAM,IAAI,OAAO,KAAK;wCAC5B,IAAI,WAAW,GAAG;wCAClB,IAAI,MAAM;2EAAG,IAAM,QAAQ;oDAAE,IAAI,IAAI,EAAE;oDAAE,OAAO;gDAAI;;wCACpD,IAAI,OAAO,GAAG;wCACd,IAAI,GAAG,GAAG,IAAI,GAAG;oCACnB;;4BAEJ;wBACF;;oBAEA,wBAAwB;oBACxB,IAAI,EAAA,yBAAA,WAAW,UAAU,cAArB,6CAAA,uBAAuB,IAAI,MAAK,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE;wBACpE,cAAc,IAAI,CAChB,IAAI;2DAAiD,CAAC,SAAS;gCAC7D,MAAM,MAAM,IAAI,OAAO,KAAK;gCAC5B,IAAI,WAAW,GAAG;gCAClB,IAAI,MAAM;mEAAG,IAAM,QAAQ;4CAAE,IAAI;4CAAc,OAAO;wCAAI;;gCAC1D,IAAI,OAAO,GAAG;gCACd,IAAI,GAAG,GAAG,WAAW,UAAU,CAAE,KAAK;4BACxC;;oBAEJ;oBAEA,IAAI,cAAc,MAAM,GAAG,GAAG;wBAC5B,IAAI;4BACF,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;4BACvC,MAAM,WAAW,aAAa,MAAM;wEAAC,CAAC;wCAAK,EAAE,EAAE,EAAE,KAAK,EAAE;oCACtD,GAAG,CAAC,GAAG,GAAG;oCACV,OAAO;gCACT;uEAAG,CAAC;4BAEJ;+DAAU,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,GAAG,QAAQ;oCAAC,CAAC;;wBAC7C,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,yBAAyB;wBACzC;oBACF;gBACF;;YAEA;QACF;2BAAG;QAAC,WAAW,OAAO;QAAE,WAAW,UAAU;QAAE;KAAO;IAEtD,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,eAAe,OAAO,IAAI,kBAAkB;gBAC9C,MAAM,QAAQ,SAAS,OAAO;gBAC9B,IAAI,OAAO;oBACT,MAAM,eAAe,MAAM,OAAO,CAAC,AAAC,IAAoB,OAAjB;oBACvC,IAAI,cAAc;4BAEhB;wBADA,eAAe,OAAO,CAAC,KAAK,CAAC;4BAAC;yBAAa;yBAC3C,mCAAA,eAAe,OAAO,CAAC,QAAQ,gBAA/B,uDAAA,iCAAmC,SAAS;oBAC9C;gBACF;YACF,OAAO,IAAI,eAAe,OAAO,EAAE;oBAEjC;gBADA,eAAe,OAAO,CAAC,KAAK,CAAC,EAAE;iBAC/B,oCAAA,eAAe,OAAO,CAAC,QAAQ,gBAA/B,wDAAA,kCAAmC,SAAS;YAC9C;QACF;2BAAG;QAAC;KAAiB;IAErB,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,CAAC,QAAQ,IAAI;YACpC,aAAa;YACb;QACF;QAEA,MAAM,YAAY,EAAE,MAAM,CAAC,EAAE;QAC7B,IAAI,aAAa,SAAS,UAAU;YAClC,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC,GAAsC;QAC3D,UAAU,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxC;IAEA,MAAM,qBAAqB,CAAC,GAAkC;QAC5D,MAAM,OAAO,EAAE,MAAM;QACrB,MAAM,SAAS,KAAK,MAAM;QAC1B,MAAM,SAAS,KAAK,MAAM;QAE1B,wCAAwC;QACxC,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC;QAEZ,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK;QAC5C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,KAAK;QAE9C,YAAY,IAAI,UAAU;QAC1B,YAAY,IAAI,KAAK,QAAQ;QAC7B,UAAU,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;IAChC;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,WAAW,UAAU,EAAE,OAAO;QAEnC,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,SAAS;YAC1C,qBACE,6LAAC,yJAAA,CAAA,OAAI;gBACH,OAAO;gBACP,QAAQ;gBACR,MAAM,WAAW,UAAU,CAAC,KAAK;;;;;;QAGvC;QAEA,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,YAAY;YAC7C,uDAAuD;YACvD,qBACE,6LAAC,yJAAA,CAAA,OAAI;gBACH,OAAO;gBACP,QAAQ;gBACR,8BAA8B;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBAC3C,4BAA4B;oBAAE,GAAG;oBAAO,GAAG;gBAAO;gBAClD,8BAA8B;oBAAC;oBAAG;oBAAW;oBAAG;iBAAU;;;;;;QAGhE;QAEA,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,WAAW,MAAM,CAAC,aAAa,EAAE;YAClE,qBACE,6LAAC,yJAAA,CAAA,QAAU;gBACT,OAAO,MAAM,CAAC,aAAa;gBAC3B,OAAO;gBACP,QAAQ;gBACR,GAAG;gBACH,GAAG;;;;;;QAGT;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,OAAO,WAAW,OAAO,CACtB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,OAAO;YAEnB,qBACE,6LAAC,yJAAA,CAAA,QAAU;gBAET,IAAI,IAAI,EAAE;gBACV,OAAO;gBACP,GAAG,IAAI,CAAC;gBACR,GAAG,IAAI,CAAC;gBACR,OAAO,IAAI,KAAK;gBAChB,QAAQ,IAAI,MAAM;gBAClB,UAAU,IAAI,QAAQ;gBACtB,SAAS,IAAI,OAAO;gBACpB,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI;gBACzB,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI;gBACzB,WAAW,SAAS;gBACpB,WAAW,CAAC,IAAM,cAAc,GAAG,IAAI,EAAE;gBACzC,gBAAgB,CAAC,IAAM,mBAAmB,GAAG,IAAI,EAAE;gBACnD,SAAS,IAAM,SAAS,YAAY,aAAa,IAAI,EAAE;eAdlD,IAAI,EAAE;;;;;QAiBjB;IACJ;IAEA,MAAM,cAAc;QAClB,OAAO,WAAW,KAAK,CACpB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,GAAG,CAAC,CAAC;gBAkBa,cACD,eACG,eACA;iCApBnB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;kCACb,6LAAC,yJAAA,CAAA,OAAI;wBACH,IAAI,KAAK,EAAE;wBACX,MAAM,KAAK,OAAO;wBAClB,GAAG,KAAK,CAAC;wBACT,GAAG,KAAK,CAAC;wBACT,OAAO,KAAK,KAAK;wBACjB,QAAQ,KAAK,MAAM;wBACnB,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,WAAW,KAAK,UAAU;wBAC1B,MAAM,KAAK,KAAK;wBAChB,OAAO,KAAK,SAAS;wBACrB,UAAU,KAAK,QAAQ;wBACvB,SAAS,KAAK,OAAO;wBACrB,QAAQ,KAAK,MAAM;wBACnB,aAAa,KAAK,WAAW;wBAC7B,WAAW,GAAE,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,KAAK;wBAC/B,UAAU,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,IAAI;wBAC7B,aAAa,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,OAAO;wBACnC,aAAa,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,OAAO;wBACnC,WAAW,SAAS;wBACpB,WAAW,CAAC,IAAM,cAAc,GAAG,KAAK,EAAE;wBAC1C,gBAAgB,CAAC,IAAM,mBAAmB,GAAG,KAAK,EAAE;wBACpD,SAAS;4BACP,sBAAsB;4BACtB,IAAI,SAAS,UAAU;gCACrB,QAAQ;4BACV;4BACA,aAAa,KAAK,EAAE;wBACtB;;;;;;oBAGD,qBAAqB,KAAK,EAAE,kBAC3B,6LAAC,yJAAA,CAAA,OAAI;wBACH,GAAG,KAAK,CAAC,GAAG;wBACZ,GAAG,KAAK,CAAC,GAAG;wBACZ,OAAO,CAAC,KAAK,KAAK,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,QAAQ,GAAG,GAAG,IAAI;wBACnE,QAAQ,CAAC,KAAK,MAAM,IAAI,KAAK,QAAQ,GAAG,GAAG,IAAI;wBAC/C,QAAO;wBACP,aAAa;wBACb,MAAM;4BAAC;4BAAG;yBAAE;wBACZ,MAAK;wBACL,WAAW;;;;;;;eA3CI,KAAK,EAAE;;;;;;IAgDlC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yJAAA,CAAA,QAAK;YACJ,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;;8BAEP,6LAAC,yJAAA,CAAA,QAAK;;wBACH;wBACA;wBACA;;;;;;;8BAEH,6LAAC,yJAAA,CAAA,QAAK;8BACJ,cAAA,6LAAC,yJAAA,CAAA,cAAW;wBACV,KAAK;wBACL,cAAc,CAAC,QAAQ;4BACrB,eAAe;4BACf,IAAI,OAAO,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;gCACzC,OAAO;4BACT;4BACA,OAAO;wBACT;;;;;;;;;;;;;;;;;;;;;;AAMZ;GA7QgB;;QASV,4HAAA,CAAA,YAAS;;;KATC", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/templateService.ts"], "sourcesContent": ["import { createClientComponentClient } from './supabase'\nimport { Template, CanvasData } from '@/types'\n\nexport class TemplateService {\n  private supabase = createClientComponentClient()\n\n  // 儲存樣板\n  async saveTemplate(\n    title: string,\n    description: string,\n    canvasData: CanvasData,\n    category: string[],\n    tags: string[],\n    previewUrl?: string\n  ): Promise<Template> {\n    const { data: { user } } = await this.supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    const templateData = {\n      title,\n      description,\n      canvas_data: canvasData,\n      category,\n      tags,\n      preview_url: previewUrl,\n      author_id: user.id,\n      is_public: false,\n      status: 'draft' as const\n    }\n\n    const { data, error } = await this.supabase\n      .from('templates')\n      .insert(templateData)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Template\n  }\n\n  // 更新樣板\n  async updateTemplate(\n    id: string,\n    updates: Partial<Template>\n  ): Promise<Template> {\n    const { data, error } = await this.supabase\n      .from('templates')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Template\n  }\n\n  // 刪除樣板\n  async deleteTemplate(id: string): Promise<void> {\n    const { error } = await this.supabase\n      .from('templates')\n      .delete()\n      .eq('id', id)\n\n    if (error) throw error\n  }\n\n  // 獲取公開樣板\n  async getPublicTemplates(\n    page = 1,\n    limit = 12,\n    category?: string,\n    tags?: string[],\n    sortBy = 'created_at'\n  ): Promise<{ templates: Template[]; total: number }> {\n    const offset = (page - 1) * limit\n\n    let query = this.supabase\n      .from('templates')\n      .select(`\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `, { count: 'exact' })\n      .eq('is_public', true)\n      .eq('status', 'approved')\n      .range(offset, offset + limit - 1)\n\n    // 分類篩選\n    if (category) {\n      query = query.contains('category', [category])\n    }\n\n    // 標籤篩選\n    if (tags && tags.length > 0) {\n      query = query.overlaps('tags', tags)\n    }\n\n    // 排序\n    switch (sortBy) {\n      case 'popular':\n        query = query.order('usage_count', { ascending: false })\n        break\n      case 'rating':\n        query = query.order('rating', { ascending: false })\n        break\n      case 'newest':\n        query = query.order('created_at', { ascending: false })\n        break\n      default:\n        query = query.order('created_at', { ascending: false })\n    }\n\n    const { data, error, count } = await query\n\n    if (error) throw error\n\n    return {\n      templates: data as Template[],\n      total: count || 0\n    }\n  }\n\n  // 獲取使用者樣板\n  async getUserTemplates(userId?: string): Promise<Template[]> {\n    let targetUserId = userId\n\n    if (!targetUserId) {\n      const { data: { user } } = await this.supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n      targetUserId = user.id\n    }\n\n    const { data, error } = await this.supabase\n      .from('templates')\n      .select('*')\n      .eq('author_id', targetUserId)\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return data as Template[]\n  }\n\n  // 獲取單個樣板\n  async getTemplate(id: string): Promise<Template> {\n    const { data, error } = await this.supabase\n      .from('templates')\n      .select(`\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `)\n      .eq('id', id)\n      .single()\n\n    if (error) throw error\n    return data as Template\n  }\n\n  // 複製樣板\n  async duplicateTemplate(templateId: string): Promise<Template> {\n    const template = await this.getTemplate(templateId)\n    \n    const { data: { user } } = await this.supabase.auth.getUser()\n    if (!user) throw new Error('User not authenticated')\n\n    const duplicatedTemplate = {\n      title: `${template.title} (副本)`,\n      description: template.description,\n      canvas_data: template.data,\n      category: template.category,\n      tags: template.tags,\n      author_id: user.id,\n      is_public: false,\n      status: 'draft' as const\n    }\n\n    const { data, error } = await this.supabase\n      .from('templates')\n      .insert(duplicatedTemplate)\n      .select()\n      .single()\n\n    if (error) throw error\n    return data as Template\n  }\n\n  // 增加使用次數\n  async incrementUsage(templateId: string): Promise<void> {\n    const { error } = await this.supabase.rpc('increment_template_usage', {\n      template_uuid: templateId\n    })\n\n    if (error) throw error\n  }\n\n  // 搜尋樣板\n  async searchTemplates(\n    query: string,\n    page = 1,\n    limit = 12\n  ): Promise<{ templates: Template[]; total: number }> {\n    const offset = (page - 1) * limit\n\n    const { data, error, count } = await this.supabase\n      .from('templates')\n      .select(`\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `, { count: 'exact' })\n      .eq('is_public', true)\n      .eq('status', 'approved')\n      .or(`title.ilike.%${query}%,description.ilike.%${query}%`)\n      .range(offset, offset + limit - 1)\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n\n    return {\n      templates: data as Template[],\n      total: count || 0\n    }\n  }\n\n  // 獲取熱門樣板\n  async getPopularTemplates(limit = 10): Promise<Template[]> {\n    const { data, error } = await this.supabase.rpc('get_popular_templates', {\n      limit_count: limit\n    })\n\n    if (error) throw error\n    return data as Template[]\n  }\n\n  // 獲取分類列表\n  async getCategories(): Promise<{ category: string; count: number }[]> {\n    const { data, error } = await this.supabase\n      .from('templates')\n      .select('category')\n      .eq('is_public', true)\n      .eq('status', 'approved')\n\n    if (error) throw error\n\n    // 統計分類\n    const categoryCount: { [key: string]: number } = {}\n    data.forEach((template: any) => {\n      template.category.forEach((cat: string) => {\n        categoryCount[cat] = (categoryCount[cat] || 0) + 1\n      })\n    })\n\n    return Object.entries(categoryCount).map(([category, count]) => ({\n      category,\n      count\n    }))\n  }\n\n  // 獲取標籤列表\n  async getTags(): Promise<{ tag: string; count: number }[]> {\n    const { data, error } = await this.supabase\n      .from('templates')\n      .select('tags')\n      .eq('is_public', true)\n      .eq('status', 'approved')\n\n    if (error) throw error\n\n    // 統計標籤\n    const tagCount: { [key: string]: number } = {}\n    data.forEach((template: any) => {\n      template.tags.forEach((tag: string) => {\n        tagCount[tag] = (tagCount[tag] || 0) + 1\n      })\n    })\n\n    return Object.entries(tagCount).map(([tag, count]) => ({\n      tag,\n      count\n    }))\n  }\n}\n\nexport const templateService = new TemplateService()\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM;IAGX,OAAO;IACP,MAAM,aACJ,KAAa,EACb,WAAmB,EACnB,UAAsB,EACtB,QAAkB,EAClB,IAAc,EACd,UAAmB,EACA;QACnB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC3D,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,MAAM,eAAe;YACnB;YACA;YACA,aAAa;YACb;YACA;YACA,aAAa;YACb,WAAW,KAAK,EAAE;YAClB,WAAW;YACX,QAAQ;QACV;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,eACJ,EAAU,EACV,OAA0B,EACP;QACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,eAAe,EAAU,EAAiB;QAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,SAAS;IACT,MAAM,qBAM+C;YALnD,OAAA,iEAAO,GACP,QAAA,iEAAQ,IACR,yDACA,qDACA,SAAA,iEAAS;QAET,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,IAAI,QAAQ,IAAI,CAAC,QAAQ,CACtB,IAAI,CAAC,aACL,MAAM,CAAE,iJAQN;YAAE,OAAO;QAAQ,GACnB,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,UAAU,YACb,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,OAAO;QACP,IAAI,UAAU;YACZ,QAAQ,MAAM,QAAQ,CAAC,YAAY;gBAAC;aAAS;QAC/C;QAEA,OAAO;QACP,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACjC;QAEA,KAAK;QACL,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAM;gBACtD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAM;gBACjD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBACrD;YACF;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;QACzD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAErC,IAAI,OAAO,MAAM;QAEjB,OAAO;YACL,WAAW;YACX,OAAO,SAAS;QAClB;IACF;IAEA,UAAU;IACV,MAAM,iBAAiB,MAAe,EAAuB;QAC3D,IAAI,eAAe;QAEnB,IAAI,CAAC,cAAc;YACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC3D,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAC3B,eAAe,KAAK,EAAE;QACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,cAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,YAAY,EAAU,EAAqB;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAE,iJASR,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,kBAAkB,UAAkB,EAAqB;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAExC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC3D,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,MAAM,qBAAqB;YACzB,OAAO,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;YACzB,aAAa,SAAS,WAAW;YACjC,aAAa,SAAS,IAAI;YAC1B,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI;YACnB,WAAW,KAAK,EAAE;YAClB,WAAW;YACX,QAAQ;QACV;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,oBACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,eAAe,UAAkB,EAAiB;QACtD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,4BAA4B;YACpE,eAAe;QACjB;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,MAAM,gBACJ,KAAa,EAGsC;YAFnD,OAAA,iEAAO,GACP,QAAA,iEAAQ;QAER,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAC/C,IAAI,CAAC,aACL,MAAM,CAAE,iJAQN;YAAE,OAAO;QAAQ,GACnB,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,UAAU,YACb,EAAE,CAAC,AAAC,gBAA4C,OAA7B,OAAM,yBAA6B,OAAN,OAAM,MACtD,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QAEjB,OAAO;YACL,WAAW;YACX,OAAO,SAAS;QAClB;IACF;IAEA,SAAS;IACT,MAAM,sBAAqD;YAAjC,QAAA,iEAAQ;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB;YACvE,aAAa;QACf;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,gBAAgE;QACpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,YACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,UAAU;QAEhB,IAAI,OAAO,MAAM;QAEjB,OAAO;QACP,MAAM,gBAA2C,CAAC;QAClD,KAAK,OAAO,CAAC,CAAC;YACZ,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACzB,aAAa,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,IAAI;YACnD;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC;gBAAC,CAAC,UAAU,MAAM;mBAAM;gBAC/D;gBACA;YACF;;IACF;IAEA,SAAS;IACT,MAAM,UAAqD;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,aACL,MAAM,CAAC,QACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,UAAU;QAEhB,IAAI,OAAO,MAAM;QAEjB,OAAO;QACP,MAAM,WAAsC,CAAC;QAC7C,KAAK,OAAO,CAAC,CAAC;YACZ,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrB,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI;YACzC;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC;gBAAC,CAAC,KAAK,MAAM;mBAAM;gBACrD;gBACA;YACF;;IACF;;QAhSA,+KAAQ,YAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;;AAiS/C;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SaveTemplateDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useEditor } from '@/hooks/useEditor'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Badge } from '@/components/ui/badge'\nimport { templateService } from '@/lib/templateService'\nimport { Save, X } from 'lucide-react'\n\ninterface SaveTemplateDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSaved?: (templateId: string) => void\n}\n\nconst CATEGORY_OPTIONS = [\n  '早安', '午安', '晚安', '勵志', '關心', '祝福', \n  '節慶', '生日', '感謝', '道歉', '加油', '問候'\n]\n\nconst TAG_SUGGESTIONS = [\n  '溫馨', '可愛', '正能量', '勵志', '感動', '幽默',\n  '清新', '優雅', '活潑', '溫暖', '美好', '希望'\n]\n\nexport function SaveTemplateDialog({ open, onOpenChange, onSaved }: SaveTemplateDialogProps) {\n  const { canvasData } = useEditor()\n  const { user } = useAuth()\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [selectedCategories, setSelectedCategories] = useState<string[]>([])\n  const [selectedTags, setSelectedTags] = useState<string[]>([])\n  const [customTag, setCustomTag] = useState('')\n  const [isPublic, setIsPublic] = useState(false)\n  const [saving, setSaving] = useState(false)\n\n  const handleSave = async () => {\n    if (!title.trim()) {\n      alert('請輸入樣板標題')\n      return\n    }\n\n    if (!user) {\n      alert('請先登入')\n      return\n    }\n\n    setSaving(true)\n    try {\n      // TODO: 生成預覽圖\n      const previewUrl = 'https://via.placeholder.com/400x300'\n\n      const template = await templateService.saveTemplate(\n        title,\n        description,\n        canvasData,\n        selectedCategories,\n        selectedTags,\n        previewUrl\n      )\n\n      onSaved?.(template.id)\n      onOpenChange(false)\n      \n      // 重置表單\n      setTitle('')\n      setDescription('')\n      setSelectedCategories([])\n      setSelectedTags([])\n      setCustomTag('')\n      setIsPublic(false)\n    } catch (error) {\n      console.error('Failed to save template:', error)\n      alert('儲存失敗，請稍後再試')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const toggleCategory = (category: string) => {\n    setSelectedCategories(prev =>\n      prev.includes(category)\n        ? prev.filter(c => c !== category)\n        : [...prev, category]\n    )\n  }\n\n  const toggleTag = (tag: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tag)\n        ? prev.filter(t => t !== tag)\n        : [...prev, tag]\n    )\n  }\n\n  const addCustomTag = () => {\n    if (customTag.trim() && !selectedTags.includes(customTag.trim())) {\n      setSelectedTags(prev => [...prev, customTag.trim()])\n      setCustomTag('')\n    }\n  }\n\n  const removeTag = (tag: string) => {\n    setSelectedTags(prev => prev.filter(t => t !== tag))\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Save className=\"h-5 w-5\" />\n            儲存為樣板\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* 基本資訊 */}\n          <div className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"title\">樣板標題 *</Label>\n              <Input\n                id=\"title\"\n                value={title}\n                onChange={(e) => setTitle(e.target.value)}\n                placeholder=\"為您的樣板取個好名字\"\n                className=\"mt-1\"\n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"description\">樣板描述</Label>\n              <Textarea\n                id=\"description\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"簡單描述這個樣板的特色和用途\"\n                className=\"mt-1\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          {/* 分類選擇 */}\n          <div>\n            <Label>分類標籤</Label>\n            <div className=\"flex flex-wrap gap-2 mt-2\">\n              {CATEGORY_OPTIONS.map(category => (\n                <Badge\n                  key={category}\n                  variant={selectedCategories.includes(category) ? 'default' : 'outline'}\n                  className=\"cursor-pointer\"\n                  onClick={() => toggleCategory(category)}\n                >\n                  {category}\n                </Badge>\n              ))}\n            </div>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              選擇適合的分類，幫助其他人找到您的樣板\n            </p>\n          </div>\n\n          {/* 標籤選擇 */}\n          <div>\n            <Label>風格標籤</Label>\n            <div className=\"flex flex-wrap gap-2 mt-2\">\n              {TAG_SUGGESTIONS.map(tag => (\n                <Badge\n                  key={tag}\n                  variant={selectedTags.includes(tag) ? 'default' : 'outline'}\n                  className=\"cursor-pointer\"\n                  onClick={() => toggleTag(tag)}\n                >\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n\n            {/* 自訂標籤 */}\n            <div className=\"flex gap-2 mt-2\">\n              <Input\n                value={customTag}\n                onChange={(e) => setCustomTag(e.target.value)}\n                placeholder=\"新增自訂標籤\"\n                onKeyPress={(e) => e.key === 'Enter' && addCustomTag()}\n                className=\"flex-1\"\n              />\n              <Button\n                variant=\"outline\"\n                onClick={addCustomTag}\n                disabled={!customTag.trim()}\n              >\n                新增\n              </Button>\n            </div>\n\n            {/* 已選標籤 */}\n            {selectedTags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {selectedTags.map(tag => (\n                  <Badge key={tag} variant=\"secondary\" className=\"flex items-center gap-1\">\n                    {tag}\n                    <X\n                      className=\"h-3 w-3 cursor-pointer\"\n                      onClick={() => removeTag(tag)}\n                    />\n                  </Badge>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* 公開設定 */}\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"isPublic\"\n              checked={isPublic}\n              onChange={(e) => setIsPublic(e.target.checked)}\n              className=\"rounded\"\n            />\n            <Label htmlFor=\"isPublic\" className=\"text-sm\">\n              公開分享（其他使用者可以看到並使用這個樣板）\n            </Label>\n          </div>\n\n          {/* 預覽資訊 */}\n          <div className=\"bg-gray-50 p-4 rounded-lg\">\n            <h4 className=\"font-medium mb-2\">樣板預覽</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <p>畫布尺寸: {canvasData.width} x {canvasData.height}</p>\n              <p>文字圖層: {canvasData.texts.length} 個</p>\n              <p>物件圖層: {canvasData.objects.length} 個</p>\n              <p>背景: {canvasData.background?.type || '無'}</p>\n            </div>\n          </div>\n\n          {/* 操作按鈕 */}\n          <div className=\"flex justify-end gap-3\">\n            <Button\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={saving}\n            >\n              取消\n            </Button>\n            <Button\n              onClick={handleSave}\n              disabled={saving || !title.trim()}\n            >\n              {saving ? '儲存中...' : '儲存樣板'}\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAZA;;;;;;;;;;;;AAoBA,MAAM,mBAAmB;IACvB;IAAM;IAAM;IAAM;IAAM;IAAM;IAC9B;IAAM;IAAM;IAAM;IAAM;IAAM;CAC/B;AAED,MAAM,kBAAkB;IACtB;IAAM;IAAM;IAAO;IAAM;IAAM;IAC/B;IAAM;IAAM;IAAM;IAAM;IAAM;CAC/B;AAEM,SAAS,mBAAmB,KAAwD;QAAxD,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAA2B,GAAxD;QAiNb;;IAhNpB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,MAAM;YACN;QACF;QAEA,IAAI,CAAC,MAAM;YACT,MAAM;YACN;QACF;QAEA,UAAU;QACV,IAAI;YACF,cAAc;YACd,MAAM,aAAa;YAEnB,MAAM,WAAW,MAAM,gIAAA,CAAA,kBAAe,CAAC,YAAY,CACjD,OACA,aACA,YACA,oBACA,cACA;YAGF,oBAAA,8BAAA,QAAU,SAAS,EAAE;YACrB,aAAa;YAEb,OAAO;YACP,SAAS;YACT,eAAe;YACf,sBAAsB,EAAE;YACxB,gBAAgB,EAAE;YAClB,aAAa;YACb,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,YACvB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,OACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,OACvB;mBAAI;gBAAM;aAAI;IAEtB;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU,IAAI,MAAM,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,KAAK;YAChE,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM,UAAU,IAAI;iBAAG;YACnD,aAAa;QACf;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;IACjD;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAS,mBAAmB,QAAQ,CAAC,YAAY,YAAY;4CAC7D,WAAU;4CACV,SAAS,IAAM,eAAe;sDAE7B;2CALI;;;;;;;;;;8CASX,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,6LAAC;;8CACC,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAA,oBACnB,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAS,aAAa,QAAQ,CAAC,OAAO,YAAY;4CAClD,WAAU;4CACV,SAAS,IAAM,UAAU;sDAExB;2CALI;;;;;;;;;;8CAWX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,aAAY;4CACZ,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4CACxC,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,CAAC,UAAU,IAAI;sDAC1B;;;;;;;;;;;;gCAMF,aAAa,MAAM,GAAG,mBACrB,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAA,oBAChB,6LAAC,oIAAA,CAAA,QAAK;4CAAW,SAAQ;4CAAY,WAAU;;gDAC5C;8DACD,6LAAC,+LAAA,CAAA,IAAC;oDACA,WAAU;oDACV,SAAS,IAAM,UAAU;;;;;;;2CAJjB;;;;;;;;;;;;;;;;sCAapB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,OAAO;oCAC7C,WAAU;;;;;;8CAEZ,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAU;;;;;;;;;;;;sCAMhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAE;gDAAO,WAAW,KAAK;gDAAC;gDAAI,WAAW,MAAM;;;;;;;sDAChD,6LAAC;;gDAAE;gDAAO,WAAW,KAAK,CAAC,MAAM;gDAAC;;;;;;;sDAClC,6LAAC;;gDAAE;gDAAO,WAAW,OAAO,CAAC,MAAM;gDAAC;;;;;;;sDACpC,6LAAC;;gDAAE;gDAAK,EAAA,yBAAA,WAAW,UAAU,cAArB,6CAAA,uBAAuB,IAAI,KAAI;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,UAAU,CAAC,MAAM,IAAI;8CAE9B,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;GAzOgB;;QACS,4HAAA,CAAA,YAAS;QACf,0HAAA,CAAA,UAAO;;;KAFV", "debugId": null}}, {"offset": {"line": 2084, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName = SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,MAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,6JAAA,CAAA,aAAgB,CAG3C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,CAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAAG,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAEjF,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAyD;QAAxD,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO;yBACvD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,QAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/slider.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/exportService.ts"], "sourcesContent": ["import Konva from 'konva'\nimport { CanvasData } from '@/types'\n\nexport interface ExportOptions {\n  format: 'png' | 'jpeg'\n  quality?: number // 0-1, only for JPEG\n  width?: number\n  height?: number\n  pixelRatio?: number\n}\n\nexport class ExportService {\n  // 匯出畫布為圖片\n  static async exportCanvas(\n    canvasData: CanvasData,\n    options: ExportOptions = { format: 'png' }\n  ): Promise<string> {\n    const {\n      format = 'png',\n      quality = 0.9,\n      width = canvasData.width,\n      height = canvasData.height,\n      pixelRatio = 2\n    } = options\n\n    // 創建離屏 Konva Stage\n    const stage = new Konva.Stage({\n      container: document.createElement('div'),\n      width,\n      height\n    })\n\n    const layer = new Konva.Layer()\n    stage.add(layer)\n\n    try {\n      // 渲染背景\n      await this.renderBackground(layer, canvasData, width, height)\n\n      // 渲染物件（按 zIndex 排序）\n      const sortedObjects = [...canvasData.objects].sort((a, b) => a.zIndex - b.zIndex)\n      for (const obj of sortedObjects) {\n        await this.renderObject(layer, obj)\n      }\n\n      // 渲染文字（按 zIndex 排序）\n      const sortedTexts = [...canvasData.texts].sort((a, b) => a.zIndex - b.zIndex)\n      for (const text of sortedTexts) {\n        this.renderText(layer, text)\n      }\n\n      // 匯出圖片\n      const dataURL = stage.toDataURL({\n        mimeType: format === 'png' ? 'image/png' : 'image/jpeg',\n        quality: format === 'jpeg' ? quality : undefined,\n        pixelRatio\n      })\n\n      return dataURL\n    } finally {\n      // 清理資源\n      stage.destroy()\n    }\n  }\n\n  // 渲染背景\n  private static async renderBackground(\n    layer: Konva.Layer,\n    canvasData: CanvasData,\n    width: number,\n    height: number\n  ): Promise<void> {\n    if (!canvasData.background) return\n\n    if (canvasData.background.type === 'color') {\n      const rect = new Konva.Rect({\n        x: 0,\n        y: 0,\n        width,\n        height,\n        fill: canvasData.background.value\n      })\n      layer.add(rect)\n    } else if (canvasData.background.type === 'image') {\n      const image = await this.loadImage(canvasData.background.value)\n      const konvaImage = new Konva.Image({\n        x: 0,\n        y: 0,\n        width,\n        height,\n        image\n      })\n      layer.add(konvaImage)\n    } else if (canvasData.background.type === 'gradient') {\n      // 簡單的漸層實現\n      const rect = new Konva.Rect({\n        x: 0,\n        y: 0,\n        width,\n        height,\n        fillLinearGradientStartPoint: { x: 0, y: 0 },\n        fillLinearGradientEndPoint: { x: width, y: height },\n        fillLinearGradientColorStops: [0, '#667eea', 1, '#764ba2']\n      })\n      layer.add(rect)\n    }\n  }\n\n  // 渲染物件\n  private static async renderObject(layer: Konva.Layer, obj: any): Promise<void> {\n    const image = await this.loadImage(obj.src)\n    const konvaImage = new Konva.Image({\n      x: obj.x,\n      y: obj.y,\n      width: obj.width,\n      height: obj.height,\n      rotation: obj.rotation,\n      opacity: obj.opacity,\n      scaleX: obj.flipX ? -1 : 1,\n      scaleY: obj.flipY ? -1 : 1,\n      image\n    })\n    layer.add(konvaImage)\n  }\n\n  // 渲染文字\n  private static renderText(layer: Konva.Layer, text: any): void {\n    const konvaText = new Konva.Text({\n      x: text.x,\n      y: text.y,\n      width: text.width,\n      height: text.height,\n      text: text.content,\n      fontSize: text.fontSize,\n      fontFamily: text.fontFamily,\n      fontStyle: text.fontWeight,\n      fill: text.color,\n      align: text.textAlign,\n      rotation: text.rotation,\n      opacity: text.opacity,\n      stroke: text.stroke,\n      strokeWidth: text.strokeWidth,\n      shadowColor: text.shadow?.color,\n      shadowBlur: text.shadow?.blur,\n      shadowOffsetX: text.shadow?.offsetX,\n      shadowOffsetY: text.shadow?.offsetY\n    })\n    layer.add(konvaText)\n  }\n\n  // 載入圖片\n  private static loadImage(src: string): Promise<HTMLImageElement> {\n    return new Promise((resolve, reject) => {\n      const img = new Image()\n      img.crossOrigin = 'anonymous'\n      img.onload = () => resolve(img)\n      img.onerror = reject\n      img.src = src\n    })\n  }\n\n  // 下載圖片\n  static downloadImage(dataURL: string, filename: string): void {\n    const link = document.createElement('a')\n    link.download = filename\n    link.href = dataURL\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n  }\n\n  // 複製到剪貼簿\n  static async copyToClipboard(dataURL: string): Promise<void> {\n    try {\n      // 將 data URL 轉換為 Blob\n      const response = await fetch(dataURL)\n      const blob = await response.blob()\n      \n      // 複製到剪貼簿\n      await navigator.clipboard.write([\n        new ClipboardItem({ [blob.type]: blob })\n      ])\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error)\n      throw error\n    }\n  }\n\n  // 分享到社群媒體\n  static shareToSocial(platform: 'facebook' | 'twitter' | 'line', imageURL?: string, text?: string): void {\n    const encodedText = encodeURIComponent(text || '來看看我用早安圖產生器製作的圖片！')\n    const encodedURL = encodeURIComponent(window.location.origin)\n    \n    let shareURL = ''\n    \n    switch (platform) {\n      case 'facebook':\n        shareURL = `https://www.facebook.com/sharer/sharer.php?u=${encodedURL}&quote=${encodedText}`\n        break\n      case 'twitter':\n        shareURL = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedURL}`\n        break\n      case 'line':\n        shareURL = `https://social-plugins.line.me/lineit/share?url=${encodedURL}&text=${encodedText}`\n        break\n    }\n    \n    if (shareURL) {\n      window.open(shareURL, '_blank', 'width=600,height=400')\n    }\n  }\n\n  // 生成分享連結\n  static generateShareLink(templateId?: string): string {\n    const baseURL = window.location.origin\n    return templateId ? `${baseURL}/template/${templateId}` : baseURL\n  }\n\n  // 預覽圖片（在新視窗中開啟）\n  static previewImage(dataURL: string): void {\n    const newWindow = window.open()\n    if (newWindow) {\n      newWindow.document.write(`\n        <html>\n          <head>\n            <title>圖片預覽</title>\n            <style>\n              body {\n                margin: 0;\n                padding: 20px;\n                background: #f0f0f0;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                min-height: 100vh;\n              }\n              img {\n                max-width: 100%;\n                max-height: 100%;\n                box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n                border-radius: 8px;\n              }\n            </style>\n          </head>\n          <body>\n            <img src=\"${dataURL}\" alt=\"Generated Image\" />\n          </body>\n        </html>\n      `)\n    }\n  }\n\n  // 獲取圖片資訊\n  static getImageInfo(dataURL: string): Promise<{\n    width: number\n    height: number\n    size: number\n    format: string\n  }> {\n    return new Promise((resolve, reject) => {\n      const img = new Image()\n      img.onload = () => {\n        // 估算檔案大小（base64 編碼後的大小）\n        const base64Length = dataURL.split(',')[1].length\n        const sizeInBytes = (base64Length * 3) / 4\n        \n        // 獲取格式\n        const format = dataURL.split(';')[0].split('/')[1]\n        \n        resolve({\n          width: img.width,\n          height: img.height,\n          size: sizeInBytes,\n          format\n        })\n      }\n      img.onerror = reject\n      img.src = dataURL\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAWO,MAAM;IACX,UAAU;IACV,aAAa,aACX,UAAsB,EAEL;YADjB,UAAA,iEAAyB;YAAE,QAAQ;QAAM;QAEzC,MAAM,EACJ,SAAS,KAAK,EACd,UAAU,GAAG,EACb,QAAQ,WAAW,KAAK,EACxB,SAAS,WAAW,MAAM,EAC1B,aAAa,CAAC,EACf,GAAG;QAEJ,mBAAmB;QACnB,MAAM,QAAQ,IAAI,wIAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAC5B,WAAW,SAAS,aAAa,CAAC;YAClC;YACA;QACF;QAEA,MAAM,QAAQ,IAAI,wIAAA,CAAA,UAAK,CAAC,KAAK;QAC7B,MAAM,GAAG,CAAC;QAEV,IAAI;YACF,OAAO;YACP,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,YAAY,OAAO;YAEtD,oBAAoB;YACpB,MAAM,gBAAgB;mBAAI,WAAW,OAAO;aAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;YAChF,KAAK,MAAM,OAAO,cAAe;gBAC/B,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO;YACjC;YAEA,oBAAoB;YACpB,MAAM,cAAc;mBAAI,WAAW,KAAK;aAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;YAC5E,KAAK,MAAM,QAAQ,YAAa;gBAC9B,IAAI,CAAC,UAAU,CAAC,OAAO;YACzB;YAEA,OAAO;YACP,MAAM,UAAU,MAAM,SAAS,CAAC;gBAC9B,UAAU,WAAW,QAAQ,cAAc;gBAC3C,SAAS,WAAW,SAAS,UAAU;gBACvC;YACF;YAEA,OAAO;QACT,SAAU;YACR,OAAO;YACP,MAAM,OAAO;QACf;IACF;IAEA,OAAO;IACP,aAAqB,iBACnB,KAAkB,EAClB,UAAsB,EACtB,KAAa,EACb,MAAc,EACC;QACf,IAAI,CAAC,WAAW,UAAU,EAAE;QAE5B,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,SAAS;YAC1C,MAAM,OAAO,IAAI,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC;gBAC1B,GAAG;gBACH,GAAG;gBACH;gBACA;gBACA,MAAM,WAAW,UAAU,CAAC,KAAK;YACnC;YACA,MAAM,GAAG,CAAC;QACZ,OAAO,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,SAAS;YACjD,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,UAAU,CAAC,KAAK;YAC9D,MAAM,aAAa,IAAI,wIAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACjC,GAAG;gBACH,GAAG;gBACH;gBACA;gBACA;YACF;YACA,MAAM,GAAG,CAAC;QACZ,OAAO,IAAI,WAAW,UAAU,CAAC,IAAI,KAAK,YAAY;YACpD,UAAU;YACV,MAAM,OAAO,IAAI,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC;gBAC1B,GAAG;gBACH,GAAG;gBACH;gBACA;gBACA,8BAA8B;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBAC3C,4BAA4B;oBAAE,GAAG;oBAAO,GAAG;gBAAO;gBAClD,8BAA8B;oBAAC;oBAAG;oBAAW;oBAAG;iBAAU;YAC5D;YACA,MAAM,GAAG,CAAC;QACZ;IACF;IAEA,OAAO;IACP,aAAqB,aAAa,KAAkB,EAAE,GAAQ,EAAiB;QAC7E,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;QAC1C,MAAM,aAAa,IAAI,wIAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACjC,GAAG,IAAI,CAAC;YACR,GAAG,IAAI,CAAC;YACR,OAAO,IAAI,KAAK;YAChB,QAAQ,IAAI,MAAM;YAClB,UAAU,IAAI,QAAQ;YACtB,SAAS,IAAI,OAAO;YACpB,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI;YACzB,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI;YACzB;QACF;QACA,MAAM,GAAG,CAAC;IACZ;IAEA,OAAO;IACP,OAAe,WAAW,KAAkB,EAAE,IAAS,EAAQ;YAgB9C,cACD,eACG,eACA;QAlBjB,MAAM,YAAY,IAAI,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC;YAC/B,GAAG,KAAK,CAAC;YACT,GAAG,KAAK,CAAC;YACT,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,MAAM,KAAK,OAAO;YAClB,UAAU,KAAK,QAAQ;YACvB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,UAAU;YAC1B,MAAM,KAAK,KAAK;YAChB,OAAO,KAAK,SAAS;YACrB,UAAU,KAAK,QAAQ;YACvB,SAAS,KAAK,OAAO;YACrB,QAAQ,KAAK,MAAM;YACnB,aAAa,KAAK,WAAW;YAC7B,WAAW,GAAE,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,KAAK;YAC/B,UAAU,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,IAAI;YAC7B,aAAa,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,OAAO;YACnC,aAAa,GAAE,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,OAAO;QACrC;QACA,MAAM,GAAG,CAAC;IACZ;IAEA,OAAO;IACP,OAAe,UAAU,GAAW,EAA6B;QAC/D,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,IAAI,WAAW,GAAG;YAClB,IAAI,MAAM,GAAG,IAAM,QAAQ;YAC3B,IAAI,OAAO,GAAG;YACd,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,OAAO;IACP,OAAO,cAAc,OAAe,EAAE,QAAgB,EAAQ;QAC5D,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,SAAS;IACT,aAAa,gBAAgB,OAAe,EAAiB;QAC3D,IAAI;YACF,sBAAsB;YACtB,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS;YACT,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;gBAC9B,IAAI,cAAc;oBAAE,CAAC,KAAK,IAAI,CAAC,EAAE;gBAAK;aACvC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,UAAU;IACV,OAAO,cAAc,QAAyC,EAAE,QAAiB,EAAE,IAAa,EAAQ;QACtG,MAAM,cAAc,mBAAmB,QAAQ;QAC/C,MAAM,aAAa,mBAAmB,OAAO,QAAQ,CAAC,MAAM;QAE5D,IAAI,WAAW;QAEf,OAAQ;YACN,KAAK;gBACH,WAAW,AAAC,gDAAmE,OAApB,YAAW,WAAqB,OAAZ;gBAC/E;YACF,KAAK;gBACH,WAAW,AAAC,yCAA2D,OAAnB,aAAY,SAAkB,OAAX;gBACvE;YACF,KAAK;gBACH,WAAW,AAAC,mDAAqE,OAAnB,YAAW,UAAoB,OAAZ;gBACjF;QACJ;QAEA,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,UAAU,UAAU;QAClC;IACF;IAEA,SAAS;IACT,OAAO,kBAAkB,UAAmB,EAAU;QACpD,MAAM,UAAU,OAAO,QAAQ,CAAC,MAAM;QACtC,OAAO,aAAa,AAAC,GAAsB,OAApB,SAAQ,cAAuB,OAAX,cAAe;IAC5D;IAEA,gBAAgB;IAChB,OAAO,aAAa,OAAe,EAAQ;QACzC,MAAM,YAAY,OAAO,IAAI;QAC7B,IAAI,WAAW;YACb,UAAU,QAAQ,CAAC,KAAK,CAAC,AAAC,mpBAuBA,OAAR,SAAQ;QAI5B;IACF;IAEA,SAAS;IACT,OAAO,aAAa,OAAe,EAKhC;QACD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,IAAI,MAAM,GAAG;gBACX,wBAAwB;gBACxB,MAAM,eAAe,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM;gBACjD,MAAM,cAAc,AAAC,eAAe,IAAK;gBAEzC,OAAO;gBACP,MAAM,SAAS,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAElD,QAAQ;oBACN,OAAO,IAAI,KAAK;oBAChB,QAAQ,IAAI,MAAM;oBAClB,MAAM;oBACN;gBACF;YACF;YACA,IAAI,OAAO,GAAG;YACd,IAAI,GAAG,GAAG;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 2690, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/export/ExportDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useEditor } from '@/hooks/useEditor'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Slider } from '@/components/ui/slider'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { ExportService, ExportOptions } from '@/lib/exportService'\nimport { \n  Download, \n  Share2, \n  Copy, \n  Eye,\n  Facebook,\n  Twitter,\n  MessageCircle,\n  Link\n} from 'lucide-react'\n\ninterface ExportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\nconst EXPORT_PRESETS = [\n  { name: '原始尺寸', width: 800, height: 600 },\n  { name: 'Instagram 正方形', width: 1080, height: 1080 },\n  { name: 'Facebook 封面', width: 1200, height: 630 },\n  { name: 'Twitter 標頭', width: 1500, height: 500 },\n  { name: 'LINE 貼圖', width: 370, height: 320 },\n  { name: '手機桌布', width: 1080, height: 1920 }\n]\n\nexport function ExportDialog({ open, onOpenChange }: ExportDialogProps) {\n  const { canvasData } = useEditor()\n  const [format, setFormat] = useState<'png' | 'jpeg'>('png')\n  const [quality, setQuality] = useState(90)\n  const [width, setWidth] = useState(800)\n  const [height, setHeight] = useState(600)\n  const [pixelRatio, setPixelRatio] = useState(2)\n  const [isExporting, setIsExporting] = useState(false)\n  const [exportedImage, setExportedImage] = useState<string | null>(null)\n  const [imageInfo, setImageInfo] = useState<any>(null)\n\n  // 重置為原始尺寸\n  useEffect(() => {\n    if (open) {\n      setWidth(canvasData.width)\n      setHeight(canvasData.height)\n      setExportedImage(null)\n      setImageInfo(null)\n    }\n  }, [open, canvasData.width, canvasData.height])\n\n  // 套用預設尺寸\n  const applyPreset = (preset: typeof EXPORT_PRESETS[0]) => {\n    setWidth(preset.width)\n    setHeight(preset.height)\n  }\n\n  // 匯出圖片\n  const handleExport = async () => {\n    setIsExporting(true)\n    try {\n      const options: ExportOptions = {\n        format,\n        quality: format === 'jpeg' ? quality / 100 : undefined,\n        width,\n        height,\n        pixelRatio\n      }\n\n      const dataURL = await ExportService.exportCanvas(canvasData, options)\n      setExportedImage(dataURL)\n\n      // 獲取圖片資訊\n      const info = await ExportService.getImageInfo(dataURL)\n      setImageInfo(info)\n    } catch (error) {\n      console.error('Export failed:', error)\n      alert('匯出失敗，請稍後再試')\n    } finally {\n      setIsExporting(false)\n    }\n  }\n\n  // 下載圖片\n  const handleDownload = () => {\n    if (exportedImage) {\n      const filename = `meme-${Date.now()}.${format}`\n      ExportService.downloadImage(exportedImage, filename)\n    }\n  }\n\n  // 複製到剪貼簿\n  const handleCopyToClipboard = async () => {\n    if (exportedImage) {\n      try {\n        await ExportService.copyToClipboard(exportedImage)\n        alert('已複製到剪貼簿')\n      } catch (error) {\n        alert('複製失敗，請使用下載功能')\n      }\n    }\n  }\n\n  // 預覽圖片\n  const handlePreview = () => {\n    if (exportedImage) {\n      ExportService.previewImage(exportedImage)\n    }\n  }\n\n  // 社群分享\n  const handleSocialShare = (platform: 'facebook' | 'twitter' | 'line') => {\n    ExportService.shareToSocial(platform, exportedImage || undefined, '來看看我用早安圖產生器製作的圖片！')\n  }\n\n  // 複製分享連結\n  const handleCopyShareLink = () => {\n    const shareLink = ExportService.generateShareLink()\n    navigator.clipboard.writeText(shareLink)\n    alert('分享連結已複製到剪貼簿')\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes < 1024) return `${bytes} B`\n    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`\n    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            匯出與分享\n          </DialogTitle>\n        </DialogHeader>\n\n        <Tabs defaultValue=\"export\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"export\">匯出設定</TabsTrigger>\n            <TabsTrigger value=\"share\">分享</TabsTrigger>\n          </TabsList>\n\n          {/* 匯出設定 */}\n          <TabsContent value=\"export\" className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* 設定面板 */}\n              <div className=\"space-y-4\">\n                <h3 className=\"font-medium\">匯出設定</h3>\n\n                {/* 格式選擇 */}\n                <div>\n                  <Label>檔案格式</Label>\n                  <Select value={format} onValueChange={(value: 'png' | 'jpeg') => setFormat(value)}>\n                    <SelectTrigger className=\"mt-1\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"png\">PNG (透明背景)</SelectItem>\n                      <SelectItem value=\"jpeg\">JPEG (較小檔案)</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* JPEG 品質 */}\n                {format === 'jpeg' && (\n                  <div>\n                    <Label>品質: {quality}%</Label>\n                    <Slider\n                      value={[quality]}\n                      onValueChange={([value]) => setQuality(value)}\n                      min={10}\n                      max={100}\n                      step={5}\n                      className=\"mt-1\"\n                    />\n                  </div>\n                )}\n\n                {/* 尺寸設定 */}\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <Label>寬度 (px)</Label>\n                    <input\n                      type=\"number\"\n                      value={width}\n                      onChange={(e) => setWidth(parseInt(e.target.value) || 800)}\n                      className=\"w-full mt-1 px-3 py-2 border rounded-md\"\n                      min=\"100\"\n                      max=\"4000\"\n                    />\n                  </div>\n                  <div>\n                    <Label>高度 (px)</Label>\n                    <input\n                      type=\"number\"\n                      value={height}\n                      onChange={(e) => setHeight(parseInt(e.target.value) || 600)}\n                      className=\"w-full mt-1 px-3 py-2 border rounded-md\"\n                      min=\"100\"\n                      max=\"4000\"\n                    />\n                  </div>\n                </div>\n\n                {/* 預設尺寸 */}\n                <div>\n                  <Label>快速尺寸</Label>\n                  <div className=\"grid grid-cols-2 gap-2 mt-1\">\n                    {EXPORT_PRESETS.map((preset) => (\n                      <Button\n                        key={preset.name}\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => applyPreset(preset)}\n                        className=\"text-xs\"\n                      >\n                        {preset.name}\n                      </Button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* 解析度 */}\n                <div>\n                  <Label>解析度倍數: {pixelRatio}x</Label>\n                  <Slider\n                    value={[pixelRatio]}\n                    onValueChange={([value]) => setPixelRatio(value)}\n                    min={1}\n                    max={4}\n                    step={0.5}\n                    className=\"mt-1\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    更高的倍數會產生更清晰但更大的檔案\n                  </p>\n                </div>\n\n                {/* 匯出按鈕 */}\n                <Button\n                  onClick={handleExport}\n                  disabled={isExporting}\n                  className=\"w-full\"\n                >\n                  {isExporting ? '匯出中...' : '生成圖片'}\n                </Button>\n              </div>\n\n              {/* 預覽面板 */}\n              <div className=\"space-y-4\">\n                <h3 className=\"font-medium\">預覽</h3>\n                \n                {exportedImage ? (\n                  <div className=\"space-y-4\">\n                    <div className=\"border rounded-lg overflow-hidden\">\n                      <img\n                        src={exportedImage}\n                        alt=\"Exported\"\n                        className=\"w-full h-auto max-h-64 object-contain bg-gray-50\"\n                      />\n                    </div>\n\n                    {/* 圖片資訊 */}\n                    {imageInfo && (\n                      <div className=\"text-sm text-gray-600 space-y-1\">\n                        <p>尺寸: {imageInfo.width} x {imageInfo.height}</p>\n                        <p>格式: {imageInfo.format.toUpperCase()}</p>\n                        <p>檔案大小: {formatFileSize(imageInfo.size)}</p>\n                      </div>\n                    )}\n\n                    {/* 操作按鈕 */}\n                    <div className=\"flex gap-2\">\n                      <Button onClick={handleDownload} className=\"flex-1\">\n                        <Download className=\"h-4 w-4 mr-2\" />\n                        下載\n                      </Button>\n                      <Button variant=\"outline\" onClick={handleCopyToClipboard}>\n                        <Copy className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"outline\" onClick={handlePreview}>\n                        <Eye className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500\">\n                    點擊「生成圖片」來預覽結果\n                  </div>\n                )}\n              </div>\n            </div>\n          </TabsContent>\n\n          {/* 分享設定 */}\n          <TabsContent value=\"share\" className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <h3 className=\"font-medium\">分享到社群媒體</h3>\n              \n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleSocialShare('facebook')}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Facebook className=\"h-4 w-4 text-blue-600\" />\n                  Facebook\n                </Button>\n                \n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleSocialShare('twitter')}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Twitter className=\"h-4 w-4 text-blue-400\" />\n                  Twitter\n                </Button>\n                \n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleSocialShare('line')}\n                  className=\"flex items-center gap-2\"\n                >\n                  <MessageCircle className=\"h-4 w-4 text-green-500\" />\n                  LINE\n                </Button>\n              </div>\n\n              <div className=\"border-t pt-4\">\n                <h4 className=\"font-medium mb-2\">分享連結</h4>\n                <Button\n                  variant=\"outline\"\n                  onClick={handleCopyShareLink}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Link className=\"h-4 w-4\" />\n                  複製分享連結\n                </Button>\n              </div>\n            </div>\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;;AA2BA,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAQ,OAAO;QAAK,QAAQ;IAAI;IACxC;QAAE,MAAM;QAAiB,OAAO;QAAM,QAAQ;IAAK;IACnD;QAAE,MAAM;QAAe,OAAO;QAAM,QAAQ;IAAI;IAChD;QAAE,MAAM;QAAc,OAAO;QAAM,QAAQ;IAAI;IAC/C;QAAE,MAAM;QAAW,OAAO;QAAK,QAAQ;IAAI;IAC3C;QAAE,MAAM;QAAQ,OAAO;QAAM,QAAQ;IAAK;CAC3C;AAEM,SAAS,aAAa,KAAyC;QAAzC,EAAE,IAAI,EAAE,YAAY,EAAqB,GAAzC;;IAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR,SAAS,WAAW,KAAK;gBACzB,UAAU,WAAW,MAAM;gBAC3B,iBAAiB;gBACjB,aAAa;YACf;QACF;iCAAG;QAAC;QAAM,WAAW,KAAK;QAAE,WAAW,MAAM;KAAC;IAE9C,SAAS;IACT,MAAM,cAAc,CAAC;QACnB,SAAS,OAAO,KAAK;QACrB,UAAU,OAAO,MAAM;IACzB;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,UAAyB;gBAC7B;gBACA,SAAS,WAAW,SAAS,UAAU,MAAM;gBAC7C;gBACA;gBACA;YACF;YAEA,MAAM,UAAU,MAAM,8HAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,YAAY;YAC7D,iBAAiB;YAEjB,SAAS;YACT,MAAM,OAAO,MAAM,8HAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;YAC9C,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,IAAI,eAAe;YACjB,MAAM,WAAW,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAAU,OAAP;YACvC,8HAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,eAAe;QAC7C;IACF;IAEA,SAAS;IACT,MAAM,wBAAwB;QAC5B,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,8HAAA,CAAA,gBAAa,CAAC,eAAe,CAAC;gBACpC,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,eAAe;YACjB,8HAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QAC7B;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB,CAAC;QACzB,8HAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,UAAU,iBAAiB,WAAW;IACpE;IAEA,SAAS;IACT,MAAM,sBAAsB;QAC1B,MAAM,YAAY,8HAAA,CAAA,gBAAa,CAAC,iBAAiB;QACjD,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,OAAO,AAAC,GAAQ,OAAN,OAAM;QAClC,IAAI,QAAQ,OAAO,MAAM,OAAO,AAAC,GAA4B,OAA1B,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAG;QAC7D,OAAO,AAAC,GAAqC,OAAnC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAG;IAC/C;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAKpC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAS,WAAU;;sCACpC,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAQ;;;;;;;;;;;;sCAI7B,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAc;;;;;;0DAG5B,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAQ,eAAe,CAAC,QAA0B,UAAU;;0EACzE,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;kFACxB,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;4CAM9B,WAAW,wBACV,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;;4DAAC;4DAAK;4DAAQ;;;;;;;kEACpB,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO;4DAAC;yDAAQ;wDAChB,eAAe;gEAAC,CAAC,MAAM;mEAAK,WAAW;;wDACvC,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAMhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACtD,WAAU;gEACV,KAAI;gEACJ,KAAI;;;;;;;;;;;;kEAGR,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACvD,WAAU;gEACV,KAAI;gEACJ,KAAI;;;;;;;;;;;;;;;;;;0DAMV,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAI,WAAU;kEACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC,qIAAA,CAAA,SAAM;gEAEL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY;gEAC3B,WAAU;0EAET,OAAO,IAAI;+DANP,OAAO,IAAI;;;;;;;;;;;;;;;;0DAaxB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;;4DAAC;4DAAQ;4DAAW;;;;;;;kEAC1B,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO;4DAAC;yDAAW;wDACnB,eAAe;gEAAC,CAAC,MAAM;mEAAK,cAAc;;wDAC1C,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAM5C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,cAAc,WAAW;;;;;;;;;;;;kDAK9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAc;;;;;;4CAE3B,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK;4DACL,KAAI;4DACJ,WAAU;;;;;;;;;;;oDAKb,2BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAE;oEAAK,UAAU,KAAK;oEAAC;oEAAI,UAAU,MAAM;;;;;;;0EAC5C,6LAAC;;oEAAE;oEAAK,UAAU,MAAM,CAAC,WAAW;;;;;;;0EACpC,6LAAC;;oEAAE;oEAAO,eAAe,UAAU,IAAI;;;;;;;;;;;;;kEAK3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS;gEAAgB,WAAU;;kFACzC,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS;0EACjC,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS;0EACjC,cAAA,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;qEAKrB,6LAAC;gDAAI,WAAU;0DAAkF;;;;;;;;;;;;;;;;;;;;;;;sCASzG,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCACnC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAIhD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;kEAEV,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAI/C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;kEAEV,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAA2B;;;;;;;;;;;;;kDAKxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;GA7TgB;;QACS,4HAAA,CAAA,YAAS;;;KADlB", "debugId": null}}, {"offset": {"line": 3497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Toolbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useEditor } from '@/hooks/useEditor'\nimport { Button } from '@/components/ui/button'\nimport { SaveTemplateDialog } from '@/components/template/SaveTemplateDialog'\nimport { ExportDialog } from '@/components/export/ExportDialog'\nimport {\n  Type,\n  Image,\n  Palette,\n  Undo,\n  Redo,\n  Trash2,\n  Download,\n  Save,\n  BookOpen\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface ToolbarProps {\n  onSave?: () => void\n  onExport?: () => void\n  onOpenTemplateLibrary?: () => void\n}\n\nexport function Toolbar({ onSave, onExport, onOpenTemplateLibrary }: ToolbarProps) {\n  const {\n    tool,\n    setTool,\n    selectedObjectId,\n    deleteLayer,\n    undo,\n    redo,\n    clearCanvas,\n    history,\n    historyIndex\n  } = useEditor()\n\n  const [saveDialogOpen, setSaveDialogOpen] = useState(false)\n  const [exportDialogOpen, setExportDialogOpen] = useState(false)\n\n  const tools = [\n    { id: 'text' as const, icon: Type, label: '文字' },\n    { id: 'object' as const, icon: Image, label: '物件' },\n    { id: 'background' as const, icon: Palette, label: '背景' },\n  ]\n\n  const canUndo = historyIndex > 0\n  const canRedo = historyIndex < history.length - 1\n\n  return (\n    <div className=\"flex items-center gap-2 p-4 bg-white border-b border-gray-200\">\n      {/* Tool Selection */}\n      <div className=\"flex items-center gap-1 mr-4\">\n        {tools.map((toolItem) => {\n          const Icon = toolItem.icon\n          return (\n            <Button\n              key={toolItem.id}\n              variant={tool === toolItem.id ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setTool(toolItem.id)}\n              className={cn(\n                'flex items-center gap-2',\n                tool === toolItem.id && 'bg-blue-100 text-blue-700'\n              )}\n            >\n              <Icon className=\"h-4 w-4\" />\n              <span className=\"hidden sm:inline\">{toolItem.label}</span>\n            </Button>\n          )\n        })}\n      </div>\n\n      {/* Divider */}\n      <div className=\"w-px h-6 bg-gray-300 mr-4\" />\n\n      {/* History Controls */}\n      <div className=\"flex items-center gap-1 mr-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={undo}\n          disabled={!canUndo}\n          title=\"復原 (Ctrl+Z)\"\n        >\n          <Undo className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline ml-2\">復原</span>\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={redo}\n          disabled={!canRedo}\n          title=\"重做 (Ctrl+Y)\"\n        >\n          <Redo className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline ml-2\">重做</span>\n        </Button>\n      </div>\n\n      {/* Divider */}\n      <div className=\"w-px h-6 bg-gray-300 mr-4\" />\n\n      {/* Object Controls */}\n      {selectedObjectId && (\n        <>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => deleteLayer(selectedObjectId)}\n            className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n            title=\"刪除選中物件\"\n          >\n            <Trash2 className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline ml-2\">刪除</span>\n          </Button>\n          \n          {/* Divider */}\n          <div className=\"w-px h-6 bg-gray-300 mr-4\" />\n        </>\n      )}\n\n      {/* Canvas Controls */}\n      <div className=\"flex items-center gap-1 mr-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={clearCanvas}\n          className=\"text-orange-600 hover:text-orange-700 hover:bg-orange-50\"\n          title=\"清空畫布\"\n        >\n          <Trash2 className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline ml-2\">清空</span>\n        </Button>\n      </div>\n\n      {/* Spacer */}\n      <div className=\"flex-1\" />\n\n      {/* Action Buttons */}\n      <div className=\"flex items-center gap-2\">\n        {onOpenTemplateLibrary && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onOpenTemplateLibrary}\n            className=\"flex items-center gap-2\"\n          >\n            <BookOpen className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">樣板庫</span>\n          </Button>\n        )}\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setSaveDialogOpen(true)}\n          className=\"flex items-center gap-2\"\n        >\n          <Save className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline\">存為樣板</span>\n        </Button>\n\n        {onSave && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onSave}\n            className=\"flex items-center gap-2\"\n          >\n            <Save className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">儲存</span>\n          </Button>\n        )}\n\n        <Button\n          size=\"sm\"\n          onClick={() => setExportDialogOpen(true)}\n          className=\"flex items-center gap-2\"\n        >\n          <Download className=\"h-4 w-4\" />\n          <span className=\"hidden sm:inline\">匯出</span>\n        </Button>\n\n        {onExport && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onExport}\n            className=\"flex items-center gap-2\"\n          >\n            <Download className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">快速匯出</span>\n          </Button>\n        )}\n      </div>\n\n      <SaveTemplateDialog\n        open={saveDialogOpen}\n        onOpenChange={setSaveDialogOpen}\n        onSaved={(templateId) => {\n          console.log('Template saved:', templateId)\n        }}\n      />\n\n      <ExportDialog\n        open={exportDialogOpen}\n        onOpenChange={setExportDialogOpen}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAlBA;;;;;;;;AA0BO,SAAS,QAAQ,KAAyD;QAAzD,EAAE,MAAM,EAAE,QAAQ,EAAE,qBAAqB,EAAgB,GAAzD;;IACtB,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,gBAAgB,EAChB,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,OAAO,EACP,YAAY,EACb,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAEZ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAiB,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAK;QAC/C;YAAE,IAAI;YAAmB,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;QAAK;QAClD;YAAE,IAAI;YAAuB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAK;KACzD;IAED,MAAM,UAAU,eAAe;IAC/B,MAAM,UAAU,eAAe,QAAQ,MAAM,GAAG;IAEhD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,OAAO,SAAS,IAAI;oBAC1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAS,SAAS,SAAS,EAAE,GAAG,YAAY;wBAC5C,MAAK;wBACL,SAAS,IAAM,QAAQ,SAAS,EAAE;wBAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,SAAS,SAAS,EAAE,IAAI;;0CAG1B,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAoB,SAAS,KAAK;;;;;;;uBAV7C,SAAS,EAAE;;;;;gBAatB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,OAAM;;0CAEN,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,OAAM;;0CAEN,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;;;;;;YAGd,kCACC;;kCACE,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,YAAY;wBAC3B,WAAU;wBACV,OAAM;;0CAEN,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;oBACV,OAAM;;sCAEN,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;oBACZ,uCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;kCAIvC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,kBAAkB;wBACjC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;oBAGpC,wBACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;kCAIvC,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;oBAGpC,0BACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC,uJAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,SAAS,CAAC;oBACR,QAAQ,GAAG,CAAC,mBAAmB;gBACjC;;;;;;0BAGF,6LAAC,+IAAA,CAAA,eAAY;gBACX,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB;GA3LgB;;QAWV,4HAAA,CAAA,YAAS;;;KAXC", "debugId": null}}, {"offset": {"line": 3937, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4058, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/LayerPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useEditor } from '@/hooks/useEditor'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  Eye, \n  EyeOff, \n  Type, \n  Image, \n  Trash2, \n  ChevronUp, \n  ChevronDown,\n  Lock,\n  Unlock\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport function LayerPanel() {\n  const {\n    canvasData,\n    selectedObjectId,\n    selectObject,\n    deleteLayer,\n    bringToFront,\n    sendToBack,\n    setLayerOpacity,\n    updateTextLayer,\n    updateObjectLayer\n  } = useEditor()\n\n  // Combine and sort all layers by zIndex\n  const allLayers = [\n    ...canvasData.texts.map(text => ({ ...text, layerType: 'text' as const })),\n    ...canvasData.objects.map(obj => ({ ...obj, layerType: 'object' as const }))\n  ].sort((a, b) => b.zIndex - a.zIndex) // Highest zIndex first (top layer)\n\n  const toggleLayerVisibility = (id: string, layerType: 'text' | 'object', currentOpacity: number) => {\n    const newOpacity = currentOpacity > 0 ? 0 : 1\n    if (layerType === 'text') {\n      updateTextLayer(id, { opacity: newOpacity })\n    } else {\n      updateObjectLayer(id, { opacity: newOpacity })\n    }\n  }\n\n  const getLayerName = (layer: any) => {\n    if (layer.layerType === 'text') {\n      return layer.content.length > 20 \n        ? `${layer.content.substring(0, 20)}...` \n        : layer.content || '文字圖層'\n    } else {\n      return '圖片物件'\n    }\n  }\n\n  const getLayerIcon = (layerType: 'text' | 'object') => {\n    return layerType === 'text' ? Type : Image\n  }\n\n  if (allLayers.length === 0) {\n    return (\n      <Card className=\"w-64\">\n        <CardHeader>\n          <CardTitle className=\"text-sm\">圖層</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-sm text-gray-500 text-center py-8\">\n            尚無圖層\n          </p>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card className=\"w-64\">\n      <CardHeader>\n        <CardTitle className=\"text-sm\">圖層</CardTitle>\n      </CardHeader>\n      <CardContent className=\"p-2\">\n        <div className=\"space-y-1\">\n          {allLayers.map((layer) => {\n            const Icon = getLayerIcon(layer.layerType)\n            const isSelected = selectedObjectId === layer.id\n            const isVisible = layer.opacity > 0\n\n            return (\n              <div\n                key={layer.id}\n                className={cn(\n                  'flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50',\n                  isSelected && 'bg-blue-50 border border-blue-200'\n                )}\n                onClick={() => selectObject(layer.id)}\n              >\n                {/* Layer Icon */}\n                <Icon className=\"h-4 w-4 text-gray-600\" />\n                \n                {/* Layer Name */}\n                <span className=\"flex-1 text-sm truncate\">\n                  {getLayerName(layer)}\n                </span>\n                \n                {/* Layer Controls */}\n                <div className=\"flex items-center gap-1\">\n                  {/* Visibility Toggle */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-6 w-6 p-0\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      toggleLayerVisibility(layer.id, layer.layerType, layer.opacity)\n                    }}\n                    title={isVisible ? '隱藏圖層' : '顯示圖層'}\n                  >\n                    {isVisible ? (\n                      <Eye className=\"h-3 w-3\" />\n                    ) : (\n                      <EyeOff className=\"h-3 w-3 text-gray-400\" />\n                    )}\n                  </Button>\n                  \n                  {/* Move Up */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-6 w-6 p-0\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      bringToFront(layer.id)\n                    }}\n                    title=\"移到最上層\"\n                  >\n                    <ChevronUp className=\"h-3 w-3\" />\n                  </Button>\n                  \n                  {/* Move Down */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-6 w-6 p-0\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      sendToBack(layer.id)\n                    }}\n                    title=\"移到最下層\"\n                  >\n                    <ChevronDown className=\"h-3 w-3\" />\n                  </Button>\n                  \n                  {/* Delete */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      deleteLayer(layer.id)\n                    }}\n                    title=\"刪除圖層\"\n                  >\n                    <Trash2 className=\"h-3 w-3\" />\n                  </Button>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n        \n        {/* Layer Info */}\n        <div className=\"mt-4 pt-2 border-t border-gray-200\">\n          <p className=\"text-xs text-gray-500\">\n            共 {allLayers.length} 個圖層\n          </p>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAhBA;;;;;;AAkBO,SAAS;;IACd,MAAM,EACJ,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,UAAU,EACV,eAAe,EACf,eAAe,EACf,iBAAiB,EAClB,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAEZ,wCAAwC;IACxC,MAAM,YAAY;WACb,WAAW,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAgB,CAAC;WACrE,WAAW,OAAO,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE,GAAG,GAAG;gBAAE,WAAW;YAAkB,CAAC;KAC3E,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,mCAAmC;;IAEzE,MAAM,wBAAwB,CAAC,IAAY,WAA8B;QACvE,MAAM,aAAa,iBAAiB,IAAI,IAAI;QAC5C,IAAI,cAAc,QAAQ;YACxB,gBAAgB,IAAI;gBAAE,SAAS;YAAW;QAC5C,OAAO;YACL,kBAAkB,IAAI;gBAAE,SAAS;YAAW;QAC9C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,MAAM,SAAS,KAAK,QAAQ;YAC9B,OAAO,MAAM,OAAO,CAAC,MAAM,GAAG,KAC1B,AAAC,GAAiC,OAA/B,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,KAAI,SAClC,MAAM,OAAO,IAAI;QACvB,OAAO;YACL,OAAO;QACT;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,cAAc,SAAS,qMAAA,CAAA,OAAI,GAAG,uMAAA,CAAA,QAAK;IAC5C;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAU;;;;;;;;;;;8BAEjC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;;;;;;IAM9D;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAU;;;;;;;;;;;0BAEjC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC;4BACd,MAAM,OAAO,aAAa,MAAM,SAAS;4BACzC,MAAM,aAAa,qBAAqB,MAAM,EAAE;4BAChD,MAAM,YAAY,MAAM,OAAO,GAAG;4BAElC,qBACE,6LAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uEACA,cAAc;gCAEhB,SAAS,IAAM,aAAa,MAAM,EAAE;;kDAGpC,6LAAC;wCAAK,WAAU;;;;;;kDAGhB,6LAAC;wCAAK,WAAU;kDACb,aAAa;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,sBAAsB,MAAM,EAAE,EAAE,MAAM,SAAS,EAAE,MAAM,OAAO;gDAChE;gDACA,OAAO,YAAY,SAAS;0DAE3B,0BACC,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;yEAEf,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAKtB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,aAAa,MAAM,EAAE;gDACvB;gDACA,OAAM;0DAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAIvB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,MAAM,EAAE;gDACrB;gDACA,OAAM;0DAEN,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAIzB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,YAAY,MAAM,EAAE;gDACtB;gDACA,OAAM;0DAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BA1EjB,MAAM,EAAE;;;;;wBA+EnB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCAChC,UAAU,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;GAlKgB;;QAWV,4HAAA,CAAA,YAAS;;;KAXC", "debugId": null}}, {"offset": {"line": 4362, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/StepGuide.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  ImageIcon,\n  Type,\n  MessageSquare,\n  Upload,\n  Palette,\n  Sparkles,\n  ChevronRight\n} from 'lucide-react'\nimport { useEditor } from '@/hooks/useEditor'\nimport { TextPanel } from './TextPanel'\nimport { TextPresets } from './TextPresets'\nimport { BackgroundPanel } from './BackgroundPanel'\nimport { BackgroundLibrary } from './BackgroundLibrary'\nimport { ObjectPanel } from './ObjectPanel'\n\n// 示範物件數據 - 與 ObjectLibrary 中的相同\nconst DEMO_OBJECTS = [\n  {\n    id: '1',\n    name: '櫻花',\n    url: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?w=200&h=200&fit=crop',\n    category: 'flowers',\n    tags: ['櫻花', '花朵', '粉色', '春天']\n  },\n  {\n    id: '2',\n    name: '愛心',\n    url: 'https://images.unsplash.com/photo-1518199266791-5375a83190b7?w=200&h=200&fit=crop',\n    category: 'shapes',\n    tags: ['愛心', '形狀', '紅色', '愛情']\n  },\n  {\n    id: '3',\n    name: '蝴蝶',\n    url: 'https://images.unsplash.com/photo-1444927714506-8492d94b5ba0?w=200&h=200&fit=crop',\n    category: 'animals',\n    tags: ['蝴蝶', '動物', '昆蟲', '彩色']\n  },\n  {\n    id: '4',\n    name: '星星',\n    url: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=200&h=200&fit=crop',\n    category: 'shapes',\n    tags: ['星星', '形狀', '夜空', '閃亮']\n  },\n  {\n    id: '5',\n    name: '太陽花',\n    url: 'https://images.unsplash.com/photo-1470509037663-253afd7f0f51?w=200&h=200&fit=crop',\n    category: 'flowers',\n    tags: ['太陽花', '花朵', '黃色', '夏天']\n  },\n  {\n    id: '6',\n    name: '禮物盒',\n    url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=200&h=200&fit=crop',\n    category: 'decorations',\n    tags: ['禮物', '裝飾', '慶祝', '驚喜']\n  },\n  {\n    id: '7',\n    name: '小鳥',\n    url: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=200&h=200&fit=crop',\n    category: 'animals',\n    tags: ['小鳥', '動物', '飛行', '自由']\n  },\n  {\n    id: '8',\n    name: '雲朵',\n    url: 'https://images.unsplash.com/photo-1419833479618-c595710ac50c?w=200&h=200&fit=crop',\n    category: 'shapes',\n    tags: ['雲朵', '形狀', '天空', '白色']\n  }\n]\n\nexport function StepGuide() {\n  const [activeStep, setActiveStep] = useState(1)\n  const { tool, setTool, addTextLayer, addObjectLayer } = useEditor()\n\n  const steps = [\n    {\n      id: 1,\n      title: '物件照片',\n      description: '主角可以是任何你喜歡的！無論是你的小孩、寵物狗狗貓貓、心愛的車輛等，我們都能自動幫你去掉背景！當然，不使用任何物件作畫也是一種選擇，我們一樣能為你製作出精彩的早安圖。',\n      icon: ImageIcon,\n      color: 'bg-blue-500'\n    },\n    {\n      id: 2,\n      title: '背景圖',\n      description: '我們可以自動為你生成背景圖，或者你也可以選擇自行上傳圖片。',\n      icon: Palette,\n      color: 'bg-green-500'\n    },\n    {\n      id: 3,\n      title: '問候語',\n      description: '選擇一個你喜愛的主題，我們自動幫你生成或問候語！你也可以選擇自行輸入你想使用的問候語。',\n      icon: MessageSquare,\n      color: 'bg-purple-500'\n    }\n  ]\n\n  const handleStepClick = (stepId: number) => {\n    setActiveStep(stepId)\n    // Set appropriate tool based on step\n    switch (stepId) {\n      case 1:\n        setTool('select') // 暫時使用 select，因為 object 工具可能需要特殊處理\n        break\n      case 2:\n        setTool('background')\n        break\n      case 3:\n        setTool('text')\n        break\n    }\n  }\n\n  // 處理按鈕點擊事件\n  const handleButtonClick = (action: string, step: number) => {\n    console.log(`Button clicked: ${action} in step ${step}`)\n\n    switch (step) {\n      case 1: // 物件照片步驟\n        switch (action) {\n          case 'my-library':\n            console.log('打開我的圖庫')\n            break\n          case 'library':\n            console.log('打開圖庫')\n            break\n          case 'upload':\n            console.log('自行上傳')\n            break\n          case 'no-object':\n            console.log('無，純背景')\n            break\n        }\n        break\n      case 2: // 背景圖步驟\n        switch (action) {\n          case 'my-library':\n            console.log('打開我的背景圖庫')\n            break\n          case 'library':\n            console.log('打開背景圖庫')\n            break\n          case 'auto-generate':\n            console.log('自動生成背景')\n            break\n          case 'upload':\n            console.log('自行上傳背景')\n            break\n        }\n        break\n      case 3: // 問候語步驟\n        switch (action) {\n          case 'auto-generate':\n            console.log('自動生成問候語')\n            // 添加示例文字到畫布\n            addTextLayer('早上一聲好　事事皆美好', 100, 100)\n            break\n          case 'manual-input':\n            console.log('自行輸入問候語')\n            break\n          case 'preset-1':\n            addTextLayer('新的一天開始了，祝你有美好的一天', 100, 100)\n            break\n          case 'preset-2':\n            addTextLayer('早安！願你今天充滿陽光和快樂', 100, 100)\n            break\n          case 'preset-3':\n            addTextLayer('今天也要加油喔！', 100, 100)\n            break\n        }\n        break\n    }\n  }\n\n  // 處理物件選擇\n  const handleObjectSelect = (object: typeof DEMO_OBJECTS[0]) => {\n    console.log(`Selected object: ${object.name}`)\n    addObjectLayer(object.url, 100, 100)\n  }\n\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 1:\n        return (\n          <div className=\"space-y-4\">\n            {/* 操作按鈕 */}\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                className=\"flex-1 bg-gray-600 text-white hover:bg-gray-700\"\n                onClick={() => handleButtonClick('my-library', 1)}\n              >\n                我的圖庫\n              </Button>\n              <Button\n                className=\"flex-1 bg-blue-600 text-white hover:bg-blue-700\"\n                onClick={() => handleButtonClick('library', 1)}\n              >\n                圖庫\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"flex-1\"\n                onClick={() => handleButtonClick('upload', 1)}\n              >\n                自行上傳\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"flex-1\"\n                onClick={() => handleButtonClick('no-object', 1)}\n              >\n                無，純背景\n              </Button>\n            </div>\n\n            {/* 圖庫 */}\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-sm\">圖庫</h4>\n              <div className=\"grid grid-cols-4 gap-2\">\n                {DEMO_OBJECTS.map((object) => (\n                  <div\n                    key={object.id}\n                    className=\"aspect-square bg-gray-100 rounded border hover:border-blue-300 cursor-pointer relative overflow-hidden\"\n                    onClick={() => handleObjectSelect(object)}\n                  >\n                    <img\n                      src={object.url}\n                      alt={object.name}\n                      className=\"w-full h-full object-cover hover:scale-105 transition-transform\"\n                      onError={(e) => {\n                        // 如果圖片載入失敗，顯示佔位符\n                        const target = e.target as HTMLImageElement\n                        target.style.display = 'none'\n                        const parent = target.parentElement\n                        if (parent) {\n                          const placeholder = document.createElement('div')\n                          placeholder.className = 'w-full h-full flex items-center justify-center text-gray-400'\n                          placeholder.innerHTML = `<svg class=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path></svg>`\n                          parent.appendChild(placeholder)\n                        }\n                      }}\n                    />\n                    <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-1\">\n                      <p className=\"text-white text-xs font-medium truncate\">\n                        {object.name}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <Button\n                variant=\"ghost\"\n                className=\"w-full text-gray-500 hover:text-gray-700\"\n                onClick={() => console.log('查看更多物件')}\n              >\n                <ChevronRight className=\"h-4 w-4 mr-1\" />\n                查看更多\n              </Button>\n            </div>\n          </div>\n        )\n      case 2:\n        return (\n          <div className=\"space-y-4\">\n            {/* 操作按鈕 */}\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                className=\"flex-1 bg-gray-600 text-white hover:bg-gray-700\"\n                onClick={() => handleButtonClick('my-library', 2)}\n              >\n                我的圖庫\n              </Button>\n              <Button\n                className=\"flex-1 bg-blue-600 text-white hover:bg-blue-700\"\n                onClick={() => handleButtonClick('library', 2)}\n              >\n                圖庫\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"flex-1\"\n                onClick={() => handleButtonClick('auto-generate', 2)}\n              >\n                自動生成\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"flex-1\"\n                onClick={() => handleButtonClick('upload', 2)}\n              >\n                自行上傳\n              </Button>\n            </div>\n\n            {/* 背景圖庫 */}\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-sm\">圖庫</h4>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {/* Sample background thumbnails */}\n                {[1, 2, 3, 4, 5, 6].map((i) => (\n                  <div\n                    key={i}\n                    className=\"aspect-video bg-gradient-to-br from-orange-400 to-red-500 rounded border hover:border-blue-300 cursor-pointer relative\"\n                    onClick={() => console.log(`Selected background ${i}`)}\n                  >\n                    {/* 示例：添加一些有選中標記的背景 */}\n                    {i === 1 && (\n                      <div className=\"absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center\">\n                        <span className=\"text-white text-xs\">✓</span>\n                      </div>\n                    )}\n                    {/* 標記為火熱背景 */}\n                    {[1, 2, 3].includes(i) && (\n                      <div className=\"absolute top-1 left-1 bg-red-500 text-white text-xs px-1 rounded\">\n                        火熱背景\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n              <Button\n                variant=\"ghost\"\n                className=\"w-full text-gray-500 hover:text-gray-700\"\n                onClick={() => console.log('查看更多背景')}\n              >\n                <ChevronRight className=\"h-4 w-4 mr-1\" />\n                查看更多\n              </Button>\n            </div>\n          </div>\n        )\n      case 3:\n        return (\n          <div className=\"space-y-4\">\n            {/* 操作按鈕 */}\n            <div className=\"flex gap-2\">\n              <Button\n                className=\"flex-1 bg-blue-600 text-white hover:bg-blue-700\"\n                onClick={() => handleButtonClick('auto-generate', 3)}\n              >\n                自動生成\n              </Button>\n              <Button\n                variant=\"outline\"\n                className=\"flex-1\"\n                onClick={() => handleButtonClick('manual-input', 3)}\n              >\n                自行輸入\n              </Button>\n            </div>\n\n            {/* 問候語選項 */}\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-sm\">問候語</h4>\n              <div className=\"space-y-2\">\n                <div className=\"p-3 bg-gray-50 rounded border text-center\">\n                  <div className=\"font-medium text-lg\">早上一聲好　事事皆美好</div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  className=\"w-full justify-center py-6\"\n                  onClick={() => handleButtonClick('preset-1', 3)}\n                >\n                  新的一天開始了，祝你有美好的一天\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  className=\"w-full justify-center py-6\"\n                  onClick={() => handleButtonClick('preset-2', 3)}\n                >\n                  早安！願你今天充滿陽光和快樂\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  className=\"w-full justify-center py-6\"\n                  onClick={() => handleButtonClick('preset-3', 3)}\n                >\n                  今天也要加油喔！\n                </Button>\n              </div>\n            </div>\n          </div>\n        )\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Step Navigation */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"space-y-4\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-start gap-3\">\n              {/* Step Number */}\n              <div className={`\n                flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium\n                ${activeStep === step.id ? step.color : 'bg-gray-300'}\n                ${activeStep > step.id ? 'bg-green-500' : ''}\n                cursor-pointer transition-colors\n              `}\n              onClick={() => handleStepClick(step.id)}\n              >\n                {activeStep > step.id ? '✓' : step.id}\n              </div>\n              \n              {/* Step Content */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center gap-2 mb-1\">\n                  <step.icon className=\"h-4 w-4 text-gray-600\" />\n                  <h3 className={`font-medium text-sm ${activeStep === step.id ? 'text-blue-600' : 'text-gray-900'}`}>\n                    {step.title}\n                  </h3>\n                </div>\n                <p className=\"text-xs text-gray-600 leading-relaxed\">\n                  {step.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Step Content */}\n      <div className=\"flex-1 p-4 overflow-y-auto\">\n        <div className=\"mb-4\">\n          <div className=\"flex items-center gap-2 mb-2\">\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              Step {activeStep}\n            </Badge>\n            <h2 className=\"font-medium text-lg\">\n              {steps.find(s => s.id === activeStep)?.title}\n            </h2>\n          </div>\n        </div>\n        \n        {renderStepContent()}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAGA;AAEA;AAAA;AAAA;AAAA;AASA;;;AAlBA;;;;;;AAyBA,gCAAgC;AAChC,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAO;YAAM;YAAM;SAAK;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;CACD;AAEM,SAAS;QA6WD;;IA5Wb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAEhE,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,YAAS;YACf,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,cAAc;QACd,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,QAAQ,WAAU,mCAAmC;gBACrD;YACF,KAAK;gBACH,QAAQ;gBACR;YACF,KAAK;gBACH,QAAQ;gBACR;QACJ;IACF;IAEA,WAAW;IACX,MAAM,oBAAoB,CAAC,QAAgB;QACzC,QAAQ,GAAG,CAAC,AAAC,mBAAoC,OAAlB,QAAO,aAAgB,OAAL;QAEjD,OAAQ;YACN,KAAK;gBACH,OAAQ;oBACN,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;gBACJ;gBACA;YACF,KAAK;gBACH,OAAQ;oBACN,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;gBACJ;gBACA;YACF,KAAK;gBACH,OAAQ;oBACN,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ,YAAY;wBACZ,aAAa,eAAe,KAAK;wBACjC;oBACF,KAAK;wBACH,QAAQ,GAAG,CAAC;wBACZ;oBACF,KAAK;wBACH,aAAa,oBAAoB,KAAK;wBACtC;oBACF,KAAK;wBACH,aAAa,kBAAkB,KAAK;wBACpC;oBACF,KAAK;wBACH,aAAa,YAAY,KAAK;wBAC9B;gBACJ;gBACA;QACJ;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,AAAC,oBAA+B,OAAZ,OAAO,IAAI;QAC3C,eAAe,OAAO,GAAG,EAAE,KAAK;IAClC;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,cAAc;8CAChD;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,WAAW;8CAC7C;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,UAAU;8CAC5C;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,aAAa;8CAC/C;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,mBAAmB;;8DAElC,6LAAC;oDACC,KAAK,OAAO,GAAG;oDACf,KAAK,OAAO,IAAI;oDAChB,WAAU;oDACV,SAAS,CAAC;wDACR,iBAAiB;wDACjB,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;wDACvB,MAAM,SAAS,OAAO,aAAa;wDACnC,IAAI,QAAQ;4DACV,MAAM,cAAc,SAAS,aAAa,CAAC;4DAC3C,YAAY,SAAS,GAAG;4DACxB,YAAY,SAAS,GAAI;4DACzB,OAAO,WAAW,CAAC;wDACrB;oDACF;;;;;;8DAEF,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEACV,OAAO,IAAI;;;;;;;;;;;;2CAvBX,OAAO,EAAE;;;;;;;;;;8CA6BpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;sDAE3B,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;YAMnD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,cAAc;8CAChD;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,WAAW;8CAC7C;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,iBAAiB;8CACnD;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,UAAU;8CAC5C;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;8CAEZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACvB,6LAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,QAAQ,GAAG,CAAC,AAAC,uBAAwB,OAAF;;gDAGjD,MAAM,mBACL,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;gDAIxC;oDAAC;oDAAG;oDAAG;iDAAE,CAAC,QAAQ,CAAC,oBAClB,6LAAC;oDAAI,WAAU;8DAAmE;;;;;;;2CAZ/E;;;;;;;;;;8CAmBX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;sDAE3B,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;YAMnD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,iBAAiB;8CACnD;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,gBAAgB;8CAClD;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAAsB;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,kBAAkB,YAAY;sDAC9C;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,kBAAkB,YAAY;sDAC9C;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,kBAAkB,YAAY;sDAC9C;;;;;;;;;;;;;;;;;;;;;;;;YAOX;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAAkB,WAAU;;8CAE3B,6LAAC;oCAAI,WAAW,AAAC,yIAGb,OADA,eAAe,KAAK,EAAE,GAAG,KAAK,KAAK,GAAG,eAAc,sBACT,OAA3C,aAAa,KAAK,EAAE,GAAG,iBAAiB,IAAG;oCAG/C,SAAS,IAAM,gBAAgB,KAAK,EAAE;8CAEnC,aAAa,KAAK,EAAE,GAAG,MAAM,KAAK,EAAE;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAG,WAAW,AAAC,uBAAiF,OAA3D,eAAe,KAAK,EAAE,GAAG,kBAAkB;8DAC9E,KAAK,KAAK;;;;;;;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BAtBb,KAAK,EAAE;;;;;;;;;;;;;;;0BA+BvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAAU;wCACvC;;;;;;;8CAER,6LAAC;oCAAG,WAAU;+CACX,cAAA,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,yBAAzB,kCAAA,YAAsC,KAAK;;;;;;;;;;;;;;;;;oBAKjD;;;;;;;;;;;;;AAIT;GAtXgB;;QAE0C,4HAAA,CAAA,YAAS;;;KAFnD", "debugId": null}}, {"offset": {"line": 5141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx"], "sourcesContent": ["'use client'\n\nimport { EditorProvider } from '@/hooks/useEditor'\nimport { Canvas } from './Canvas'\nimport { Toolbar } from './Toolbar'\nimport { LayerPanel } from './LayerPanel'\nimport { StepGuide } from './StepGuide'\n\ninterface EditorProps {\n  onSave?: () => void\n  onExport?: () => void\n}\n\nexport function Editor({ onSave, onExport }: EditorProps) {\n  return (\n    <EditorProvider>\n      <div className=\"h-screen flex flex-col bg-gray-50\">\n        {/* Toolbar */}\n        <Toolbar onSave={onSave} onExport={onExport} />\n\n        {/* Main Editor Area - 2 Column Layout */}\n        <div className=\"flex-1 flex\">\n          {/* Left Panel - Steps Guide */}\n          <div className=\"w-96 border-r border-gray-200 bg-white overflow-y-auto\">\n            <StepGuide />\n          </div>\n\n          {/* Right Panel - Canvas and Layer Panel */}\n          <div className=\"flex-1 flex flex-col\">\n            {/* Canvas Area - Fixed at top, with shadow for better separation */}\n            <div className=\"bg-white border-b border-gray-200 p-4 flex justify-center shadow-sm sticky top-0 z-10\">\n              <div className=\"bg-gray-50 p-2 rounded-lg\">\n                <Canvas />\n              </div>\n            </div>\n\n            {/* Layer Panel - Below canvas, scrollable */}\n            <div className=\"flex-1 bg-white p-4 overflow-y-auto\">\n              <div className=\"max-w-md mx-auto\">\n                <LayerPanel />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </EditorProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaO,SAAS,OAAO,KAAiC;QAAjC,EAAE,MAAM,EAAE,QAAQ,EAAe,GAAjC;IACrB,qBACE,6LAAC,4HAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,0IAAA,CAAA,UAAO;oBAAC,QAAQ;oBAAQ,UAAU;;;;;;8BAGnC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4IAAA,CAAA,YAAS;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;;;;;8CAKX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B;KAlCgB", "debugId": null}}, {"offset": {"line": 5263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAC/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AACzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AACrD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AACvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AACjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,6JAAA,CAAA,aAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,OAGrC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5575, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/LoginForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2 } from 'lucide-react'\n\ninterface LoginFormProps {\n  onToggleMode: () => void\n  onSuccess?: () => void\n}\n\nexport function LoginForm({ onToggleMode, onSuccess }: LoginFormProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  \n  const { signIn } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      await signIn(email, password)\n      onSuccess?.()\n    } catch (err: any) {\n      setError(err.message || '登入失敗，請檢查您的帳號密碼')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader>\n        <CardTitle>登入</CardTitle>\n        <CardDescription>\n          登入您的帳號開始創作早安圖\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">電子郵件</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"請輸入您的電子郵件\"\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\">密碼</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"請輸入您的密碼\"\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            登入\n          </Button>\n          \n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={onToggleMode}\n              className=\"text-sm text-blue-600 hover:underline\"\n              disabled={loading}\n            >\n              還沒有帳號？立即註冊\n            </button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAgBO,SAAS,UAAU,KAA2C;QAA3C,EAAE,YAAY,EAAE,SAAS,EAAkB,GAA3C;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,OAAO,OAAO;YACpB,sBAAA,gCAAA;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;sCACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;sCAIvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,QAAQ;oCACR,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,QAAQ;oCACR,UAAU;;;;;;;;;;;;sCAId,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAS,UAAU;;gCAChD,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GApFgB;;QAMK,0HAAA,CAAA,UAAO;;;KANZ", "debugId": null}}, {"offset": {"line": 5795, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/SignUpForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2 } from 'lucide-react'\n\ninterface SignUpFormProps {\n  onToggleMode: () => void\n  onSuccess?: () => void\n}\n\nexport function SignUpForm({ onToggleMode, onSuccess }: SignUpFormProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [username, setUsername] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState(false)\n  \n  const { signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n    setSuccess(false)\n\n    // Validation\n    if (password !== confirmPassword) {\n      setError('密碼確認不符')\n      setLoading(false)\n      return\n    }\n\n    if (password.length < 6) {\n      setError('密碼長度至少需要 6 個字元')\n      setLoading(false)\n      return\n    }\n\n    try {\n      await signUp(email, password, {\n        username: username || undefined,\n        full_name: fullName || undefined\n      })\n      \n      setSuccess(true)\n      setTimeout(() => {\n        onSuccess?.()\n      }, 2000)\n    } catch (err: any) {\n      setError(err.message || '註冊失敗，請稍後再試')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (success) {\n    return (\n      <Card className=\"w-full max-w-md mx-auto\">\n        <CardContent className=\"pt-6\">\n          <Alert>\n            <AlertDescription>\n              註冊成功！請檢查您的電子郵件並點擊確認連結來啟用帳號。\n            </AlertDescription>\n          </Alert>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader>\n        <CardTitle>註冊</CardTitle>\n        <CardDescription>\n          建立新帳號開始您的創作之旅\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">電子郵件 *</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"請輸入您的電子郵件\"\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"fullName\">姓名</Label>\n            <Input\n              id=\"fullName\"\n              type=\"text\"\n              value={fullName}\n              onChange={(e) => setFullName(e.target.value)}\n              placeholder=\"請輸入您的姓名\"\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"username\">使用者名稱</Label>\n            <Input\n              id=\"username\"\n              type=\"text\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              placeholder=\"請輸入使用者名稱\"\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\">密碼 *</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"請輸入密碼 (至少 6 個字元)\"\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"confirmPassword\">確認密碼 *</Label>\n            <Input\n              id=\"confirmPassword\"\n              type=\"password\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              placeholder=\"請再次輸入密碼\"\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            註冊\n          </Button>\n          \n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={onToggleMode}\n              className=\"text-sm text-blue-600 hover:underline\"\n              disabled={loading}\n            >\n              已有帳號？立即登入\n            </button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAgBO,SAAS,WAAW,KAA4C;QAA5C,EAAE,YAAY,EAAE,SAAS,EAAmB,GAA5C;;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,aAAa;QACb,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,OAAO,OAAO,UAAU;gBAC5B,UAAU,YAAY;gBACtB,WAAW,YAAY;YACzB;YAEA,WAAW;YACX,WAAW;gBACT,sBAAA,gCAAA;YACF,GAAG;QACL,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC,oIAAA,CAAA,QAAK;8BACJ,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;;;;;;;;;;IAO5B;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;sCACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;sCAIvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,QAAQ;oCACR,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,QAAQ;oCACR,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,QAAQ;oCACR,UAAU;;;;;;;;;;;;sCAId,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAS,UAAU;;gCAChD,yBAAW,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhKgB;;QAUK,0HAAA,CAAA,UAAO;;;KAVZ", "debugId": null}}, {"offset": {"line": 6154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/AuthDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'\nimport { LoginForm } from './LoginForm'\nimport { SignUpForm } from './SignUpForm'\n\ninterface AuthDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport function AuthDialog({ open, onOpenChange, defaultMode = 'login' }: AuthDialogProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n\n  const handleToggleMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n  }\n\n  const handleSuccess = () => {\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle>{mode === 'login' ? 'Login' : 'Sign Up'}</DialogTitle>\n        </DialogHeader>\n        {mode === 'login' ? (\n          <LoginForm onToggleMode={handleToggleMode} onSuccess={handleSuccess} />\n        ) : (\n          <SignUpForm onToggleMode={handleToggleMode} onSuccess={handleSuccess} />\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaO,SAAS,WAAW,KAA8D;QAA9D,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,OAAO,EAAmB,GAA9D;;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAErD,MAAM,mBAAmB;QACvB,QAAQ,SAAS,UAAU,WAAW;IACxC;IAEA,MAAM,gBAAgB;QACpB,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAE,SAAS,UAAU,UAAU;;;;;;;;;;;gBAE5C,SAAS,wBACR,6LAAC,0IAAA,CAAA,YAAS;oBAAC,cAAc;oBAAkB,WAAW;;;;;yCAEtD,6LAAC,2IAAA,CAAA,aAAU;oBAAC,cAAc;oBAAkB,WAAW;;;;;;;;;;;;;;;;;AAKjE;GAzBgB;KAAA", "debugId": null}}, {"offset": {"line": 6238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Heart, Image } from 'lucide-react'\nimport { AuthDialog } from './AuthDialog'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const [authDialogOpen, setAuthDialogOpen] = useState(false)\n  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login')\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    }\n  }\n\n  const openAuthDialog = (mode: 'login' | 'signup') => {\n    setAuthMode(mode)\n    setAuthDialogOpen(true)\n  }\n\n  if (!user) {\n    return (\n      <>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            onClick={() => openAuthDialog('login')}\n          >\n            登入\n          </Button>\n          <Button\n            onClick={() => openAuthDialog('signup')}\n          >\n            註冊\n          </Button>\n        </div>\n        \n        <AuthDialog\n          open={authDialogOpen}\n          onOpenChange={setAuthDialogOpen}\n          defaultMode={authMode}\n        />\n      </>\n    )\n  }\n\n  const displayName = (profile as any)?.full_name || (profile as any)?.username || user.email?.split('@')[0] || '使用者'\n  const avatarFallback = displayName.charAt(0).toUpperCase()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n          <Avatar className=\"h-8 w-8\">\n            <AvatarImage src={profile?.avatar_url} alt={displayName} />\n            <AvatarFallback>{avatarFallback}</AvatarFallback>\n          </Avatar>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">{displayName}</p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>個人資料</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link href=\"/my-creations\" className=\"cursor-pointer\">\n            <Image className=\"mr-2 h-4 w-4\" />\n            <span>我的作品</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link href=\"/favorites\" className=\"cursor-pointer\">\n            <Heart className=\"mr-2 h-4 w-4\" />\n            <span>我的收藏</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link href=\"/settings\" className=\"cursor-pointer\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>設定</span>\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem onClick={handleSignOut} className=\"cursor-pointer\">\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>登出</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAhBA;;;;;;;;;AAkBO,SAAS;QA4CM,OAA+B,QAA8B;;IA3CjF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAE7D,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,kBAAkB;IACpB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE;;8BACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe;sCAC/B;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;sCAC/B;;;;;;;;;;;;8BAKH,6LAAC,2IAAA,CAAA,aAAU;oBACT,MAAM;oBACN,cAAc;oBACd,aAAa;;;;;;;;IAIrB;IAEA,MAAM,cAAc,EAAA,QAAC,qBAAD,4BAAA,MAAkB,SAAS,OAAI,SAAC,qBAAD,6BAAA,OAAkB,QAAQ,OAAI,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,KAAI;IAC9G,MAAM,iBAAiB,YAAY,MAAM,CAAC,GAAG,WAAW;IAExD,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,6LAAC,qIAAA,CAAA,cAAW;gCAAC,GAAG,EAAE,oBAAA,8BAAA,QAAS,UAAU;gCAAE,KAAK;;;;;;0CAC5C,6LAAC,qIAAA,CAAA,iBAAc;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAIvB,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAIjB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCACtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAgB,WAAU;;8CACnC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;;8CAC/B,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCACtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS;wBAAe,WAAU;;0CAClD,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAnGgB;;QACqB,0HAAA,CAAA,UAAO;;;KAD5B", "debugId": null}}]}