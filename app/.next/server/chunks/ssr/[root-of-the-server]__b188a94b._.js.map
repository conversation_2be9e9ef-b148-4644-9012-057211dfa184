{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Editor = registerClientReference(\n    function() { throw new Error(\"Attempted to call Editor() from the server but Editor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/editor/Editor.tsx <module evaluation>\",\n    \"Editor\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Editor = registerClientReference(\n    function() { throw new Error(\"Attempted to call Editor() from the server but Editor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/editor/Editor.tsx\",\n    \"Editor\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const UserMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserMenu() from the server but UserMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/UserMenu.tsx <module evaluation>\",\n    \"UserMenu\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const UserMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserMenu() from the server but UserMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/UserMenu.tsx\",\n    \"UserMenu\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8CACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/page.tsx"], "sourcesContent": ["import { Editor } from '@/components/editor/Editor'\nimport { UserMenu } from '@/components/auth/UserMenu'\n\nexport default function Home() {\n  return (\n    <div className=\"h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              GoodMorning Meme Creator\n            </h1>\n            <span className=\"text-sm text-gray-500\">早安圖產生器</span>\n          </div>\n          <UserMenu />\n        </div>\n      </header>\n\n      {/* Main Editor */}\n      <main className=\"flex-1\">\n        <Editor />\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;0BAKb,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}