{"version": 3, "sources": [], "sections": [{"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/database.ts"], "sourcesContent": ["import { createClientComponentClient, createServerComponentClient } from './supabase'\nimport { Template, User, Asset, Rating, Comment, Favorite } from '@/types'\n\n// Template operations\nexport const templateService = {\n  async getPublicTemplates(page = 1, limit = 12, category?: string, sortBy = 'created_at') {\n    const supabase = createClientComponentClient()\n    const offset = (page - 1) * limit\n    \n    let query = supabase\n      .from('templates')\n      .select(`\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `)\n      .eq('is_public', true)\n      .eq('status', 'approved')\n      .range(offset, offset + limit - 1)\n    \n    if (category) {\n      query = query.contains('category', [category])\n    }\n    \n    switch (sortBy) {\n      case 'popular':\n        query = query.order('usage_count', { ascending: false })\n        break\n      case 'rating':\n        query = query.order('rating', { ascending: false })\n        break\n      default:\n        query = query.order('created_at', { ascending: false })\n    }\n    \n    const { data, error } = await query\n    \n    if (error) throw error\n    return data as (Template & { profiles: User })[]\n  },\n\n  async getTemplateById(id: string) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('templates')\n      .select(`\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `)\n      .eq('id', id)\n      .single()\n    \n    if (error) throw error\n    return data as Template & { profiles: User }\n  },\n\n  async createTemplate(template: Omit<Template, 'id' | 'created_at' | 'updated_at'>) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('templates')\n      .insert(template)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Template\n  },\n\n  async updateTemplate(id: string, updates: Partial<Template>) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('templates')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Template\n  },\n\n  async deleteTemplate(id: string) {\n    const supabase = createClientComponentClient()\n    \n    const { error } = await supabase\n      .from('templates')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  },\n\n  async incrementUsage(id: string) {\n    const supabase = createClientComponentClient()\n    \n    const { error } = await supabase.rpc('increment_template_usage', {\n      template_uuid: id\n    })\n    \n    if (error) throw error\n  }\n}\n\n// User operations\nexport const userService = {\n  async getProfile(userId: string) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n    \n    if (error) throw error\n    return data as User\n  },\n\n  async updateProfile(userId: string, updates: Partial<User>) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('profiles')\n      .update(updates)\n      .eq('id', userId)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as User\n  },\n\n  async getUserTemplates(userId: string) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('templates')\n      .select('*')\n      .eq('author_id', userId)\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data as Template[]\n  },\n\n  async getUserFavorites(userId: string) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('user_favorites')\n      .select(`\n        *,\n        templates (\n          *,\n          profiles:author_id (\n            id,\n            username,\n            full_name,\n            avatar_url\n          )\n        )\n      `)\n      .eq('user_id', userId)\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data\n  }\n}\n\n// Asset operations\nexport const assetService = {\n  async getPublicAssets(type: 'background' | 'object', category?: string) {\n    const supabase = createClientComponentClient()\n    \n    let query = supabase\n      .from('assets')\n      .select('*')\n      .eq('type', type)\n      .eq('is_public', true)\n      .order('created_at', { ascending: false })\n    \n    if (category) {\n      query = query.eq('category', category)\n    }\n    \n    const { data, error } = await query\n    \n    if (error) throw error\n    return data as Asset[]\n  },\n\n  async createAsset(asset: Omit<Asset, 'id' | 'created_at'>) {\n    const supabase = createClientComponentClient()\n    \n    const { data, error } = await supabase\n      .from('assets')\n      .insert(asset)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data as Asset\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAIO,MAAM,kBAAkB;IAC7B,MAAM,oBAAmB,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,QAAiB,EAAE,SAAS,YAAY;QACrF,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAC3C,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,UAAU,YACb,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,UAAU;YACZ,QAAQ,MAAM,QAAQ,CAAC,YAAY;gBAAC;aAAS;QAC/C;QAEA,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAM;gBACtD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAM;gBACjD;YACF;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;QACzD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,iBAAgB,EAAU;QAC9B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,QAA4D;QAC/E,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,EAAU,EAAE,OAA0B;QACzD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4BAA4B;YAC/D,eAAe;QACjB;QAEA,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,YAAW,MAAc;QAC7B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAc,MAAc,EAAE,OAAsB;QACxD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,kBAAiB,MAAc;QACnC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,QAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,kBAAiB,MAAc;QACnC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,iBAAgB,IAA6B,EAAE,QAAiB;QACpE,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,IAAI,QAAQ,SACT,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,aAAY,KAAuC;QACvD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;QAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState, createElement } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { User as AppUser } from '@/types'\nimport { userService } from '@/lib/database'\n\ninterface AuthContextType {\n  user: User | null\n  profile: AppUser | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<void>\n  signUp: (email: string, password: string, userData?: { username?: string; full_name?: string }) => Promise<void>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<AppUser>) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<AppUser | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientComponentClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        try {\n          const userProfile = await userService.getProfile(session.user.id)\n          setProfile(userProfile)\n        } catch (error) {\n          console.error('Error fetching user profile:', error)\n        }\n      }\n      \n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          try {\n            const userProfile = await userService.getProfile(session.user.id)\n            setProfile(userProfile)\n          } catch (error) {\n            console.error('Error fetching user profile:', error)\n            setProfile(null)\n          }\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase.auth])\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    \n    if (error) throw error\n  }\n\n  const signUp = async (\n    email: string, \n    password: string, \n    userData?: { username?: string; full_name?: string }\n  ) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: userData\n      }\n    })\n    \n    if (error) throw error\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n  }\n\n  const updateProfile = async (updates: Partial<AppUser>) => {\n    if (!user) throw new Error('No user logged in')\n    \n    const updatedProfile = await userService.updateProfile(user.id, updates)\n    setProfile(updatedProfile)\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n  }\n\n  return createElement(AuthContext.Provider, { value }, children)\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AANA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAC5D,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,IAAI;oBACF,MAAM,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE;oBAChE,WAAW;gBACb,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,IAAI;oBACF,MAAM,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE;oBAChE,WAAW;gBACb,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,WAAW;gBACb;YACF,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,SAAS,OACb,OACA,UACA;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,MAAM,iBAAiB,MAAM,sHAAA,CAAA,cAAW,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;QAChE,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,QAAQ,EAAE;QAAE;IAAM,GAAG;AACxD;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}