{"version": 1, "files": ["../webpack-runtime.js", "../chunks/985.js", "../chunks/903.js", "page_client-reference-manifest.js", "../../../package.json", "../../../src/components/editor/Toolbar.tsx", "../../../src/components/editor/Canvas.tsx", "../../../src/components/editor/LayerPanel.tsx", "../../../src/components/editor/StepGuide.tsx", "../../../src/components/auth/AuthDialog.tsx", "../../../src/hooks/useEditor.ts", "../../../src/components/ui/button.tsx", "../../../src/components/ui/dropdown-menu.tsx", "../../../src/components/ui/avatar.tsx", "../../../src/components/ui/dialog.tsx", "../../../src/components/auth/LoginForm.tsx", "../../../src/components/auth/SignUpForm.tsx", "../../../src/components/ui/card.tsx", "../../../src/lib/supabase.ts", "../../../src/lib/database.ts", "../../../src/lib/utils.ts", "../../../src/components/ui/badge.tsx", "../../../src/components/editor/TextPanel.tsx", "../../../src/components/editor/TextPresets.tsx", "../../../src/components/editor/BackgroundPanel.tsx", "../../../src/components/editor/BackgroundLibrary.tsx", "../../../src/components/editor/ObjectPanel.tsx", "../../../src/components/template/SaveTemplateDialog.tsx", "../../../src/components/export/ExportDialog.tsx", "../../../src/components/ui/input.tsx", "../../../src/components/ui/label.tsx", "../../../src/components/ui/alert.tsx", "../../../src/components/ui/tabs.tsx", "../../../src/components/ui/slider.tsx", "../../../src/components/ui/select.tsx", "../../../src/components/editor/ObjectLibrary.tsx", "../../../src/components/ui/textarea.tsx", "../../../src/lib/templateService.ts", "../../../src/lib/exportService.ts"]}