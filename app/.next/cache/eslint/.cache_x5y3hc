[{"/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/layout.tsx": "1", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/page.tsx": "2", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/AuthDialog.tsx": "3", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/LoginForm.tsx": "4", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/SignUpForm.tsx": "5", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx": "6", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundLibrary.tsx": "7", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundPanel.tsx": "8", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Canvas.tsx": "9", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx": "10", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/LayerPanel.tsx": "11", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectLibrary.tsx": "12", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectPanel.tsx": "13", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPanel.tsx": "14", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPresets.tsx": "15", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Toolbar.tsx": "16", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/export/ExportDialog.tsx": "17", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SaveTemplateDialog.tsx": "18", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SubmitTemplateDialog.tsx": "19", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/TemplateLibrary.tsx": "20", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/alert.tsx": "21", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/avatar.tsx": "22", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/badge.tsx": "23", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/button.tsx": "24", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/card.tsx": "25", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dialog.tsx": "26", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dropdown-menu.tsx": "27", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/input.tsx": "28", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/label.tsx": "29", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/select.tsx": "30", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/slider.tsx": "31", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/tabs.tsx": "32", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/textarea.tsx": "33", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useAuth.ts": "34", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useEditor.ts": "35", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/database.ts": "36", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/exportService.ts": "37", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase-server.ts": "38", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase.ts": "39", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/templateService.ts": "40", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/utils.ts": "41", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/types/index.ts": "42", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/StepGuide.tsx": "43"}, {"size": 851, "mtime": 1753788536209, "results": "44", "hashOfConfig": "45"}, {"size": 777, "mtime": 1753790243708, "results": "46", "hashOfConfig": "45"}, {"size": 1154, "mtime": 1753793009726, "results": "47", "hashOfConfig": "45"}, {"size": 3003, "mtime": 1753787082785, "results": "48", "hashOfConfig": "45"}, {"size": 5249, "mtime": 1753787113425, "results": "49", "hashOfConfig": "45"}, {"size": 3735, "mtime": 1753797372680, "results": "50", "hashOfConfig": "45"}, {"size": 6678, "mtime": 1753789361524, "results": "51", "hashOfConfig": "45"}, {"size": 9593, "mtime": 1753793325969, "results": "52", "hashOfConfig": "45"}, {"size": 7445, "mtime": 1753789402527, "results": "53", "hashOfConfig": "45"}, {"size": 1299, "mtime": 1753797234538, "results": "54", "hashOfConfig": "45"}, {"size": 5594, "mtime": 1753788714508, "results": "55", "hashOfConfig": "45"}, {"size": 7036, "mtime": 1753789564796, "results": "56", "hashOfConfig": "45"}, {"size": 9817, "mtime": 1753789587128, "results": "57", "hashOfConfig": "45"}, {"size": 8752, "mtime": 1753789128602, "results": "58", "hashOfConfig": "45"}, {"size": 5103, "mtime": 1753789191445, "results": "59", "hashOfConfig": "45"}, {"size": 5938, "mtime": 1753790194673, "results": "60", "hashOfConfig": "45"}, {"size": 12348, "mtime": 1753790145065, "results": "61", "hashOfConfig": "45"}, {"size": 8258, "mtime": 1753789784541, "results": "62", "hashOfConfig": "45"}, {"size": 4370, "mtime": 1753797404915, "results": "63", "hashOfConfig": "45"}, {"size": 10176, "mtime": 1753789750801, "results": "64", "hashOfConfig": "45"}, {"size": 1583, "mtime": 1753787212600, "results": "65", "hashOfConfig": "45"}, {"size": 1404, "mtime": 1753788507964, "results": "66", "hashOfConfig": "45"}, {"size": 1127, "mtime": 1753789372026, "results": "67", "hashOfConfig": "45"}, {"size": 1834, "mtime": 1753787146979, "results": "68", "hashOfConfig": "45"}, {"size": 1876, "mtime": 1753787178991, "results": "69", "hashOfConfig": "45"}, {"size": 3833, "mtime": 1753787195793, "results": "70", "hashOfConfig": "45"}, {"size": 4977, "mtime": 1753788498099, "results": "71", "hashOfConfig": "45"}, {"size": 823, "mtime": 1753787163296, "results": "72", "hashOfConfig": "45"}, {"size": 709, "mtime": 1753787169710, "results": "73", "hashOfConfig": "45"}, {"size": 5612, "mtime": 1753789163732, "results": "74", "hashOfConfig": "45"}, {"size": 1076, "mtime": 1753789143912, "results": "75", "hashOfConfig": "45"}, {"size": 1882, "mtime": 1753789320716, "results": "76", "hashOfConfig": "45"}, {"size": 771, "mtime": 1753789791752, "results": "77", "hashOfConfig": "45"}, {"size": 3426, "mtime": 1753793138799, "results": "78", "hashOfConfig": "45"}, {"size": 12807, "mtime": 1753797330352, "results": "79", "hashOfConfig": "45"}, {"size": 5136, "mtime": 1753786001060, "results": "80", "hashOfConfig": "45"}, {"size": 7650, "mtime": 1753790096174, "results": "81", "hashOfConfig": "45"}, {"size": 2285, "mtime": 1753791011278, "results": "82", "hashOfConfig": "45"}, {"size": 322, "mtime": 1753790937834, "results": "83", "hashOfConfig": "45"}, {"size": 7105, "mtime": 1753789709779, "results": "84", "hashOfConfig": "45"}, {"size": 1138, "mtime": 1753783068731, "results": "85", "hashOfConfig": "45"}, {"size": 2237, "mtime": 1753783083414, "results": "86", "hashOfConfig": "45"}, {"size": 5920, "mtime": 1753797274021, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1eejxl", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/AuthDialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/LoginForm.tsx", ["217"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/SignUpForm.tsx", ["218"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx", ["219", "220", "221"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundLibrary.tsx", ["222", "223", "224"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundPanel.tsx", ["225", "226"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Canvas.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/LayerPanel.tsx", ["227", "228", "229", "230"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectLibrary.tsx", ["231", "232"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectPanel.tsx", ["233", "234"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPanel.tsx", ["235", "236", "237", "238"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPresets.tsx", ["239"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Toolbar.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/export/ExportDialog.tsx", ["240", "241", "242", "243"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SaveTemplateDialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SubmitTemplateDialog.tsx", ["244", "245", "246"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/TemplateLibrary.tsx", ["247", "248", "249", "250"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dropdown-menu.tsx", ["251", "252"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/input.tsx", ["253"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/select.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/slider.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/textarea.tsx", ["254"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useEditor.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/database.ts", ["255", "256", "257", "258"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/exportService.ts", ["259", "260"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase-server.ts", ["261"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/templateService.ts", ["262", "263"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/types/index.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/StepGuide.tsx", ["264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279"], [], {"ruleId": "280", "severity": 2, "message": "281", "line": 33, "column": 19, "nodeType": "282", "messageId": "283", "endLine": 33, "endColumn": 22, "suggestions": "284"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 58, "column": 19, "nodeType": "282", "messageId": "283", "endLine": 58, "endColumn": 22, "suggestions": "285"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 63, "column": 35, "nodeType": "282", "messageId": "283", "endLine": 63, "endColumn": 38, "suggestions": "286"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 63, "column": 66, "nodeType": "282", "messageId": "283", "endLine": 63, "endColumn": 69, "suggestions": "287"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 94, "column": 13, "nodeType": "290", "endLine": 94, "endColumn": 47}, {"ruleId": "291", "severity": 1, "message": "292", "line": 5, "column": 10, "nodeType": null, "messageId": "293", "endLine": 5, "endColumn": 16}, {"ruleId": "291", "severity": 1, "message": "294", "line": 10, "column": 10, "nodeType": null, "messageId": "293", "endLine": 10, "endColumn": 22}, {"ruleId": "295", "severity": 1, "message": "296", "line": 176, "column": 17, "nodeType": "290", "endLine": 180, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "297", "line": 14, "column": 12, "nodeType": null, "messageId": "293", "endLine": 14, "endColumn": 21}, {"ruleId": "295", "severity": 1, "message": "296", "line": 209, "column": 19, "nodeType": "290", "endLine": 213, "endColumn": 21}, {"ruleId": "291", "severity": 1, "message": "298", "line": 14, "column": 3, "nodeType": null, "messageId": "293", "endLine": 14, "endColumn": 7}, {"ruleId": "291", "severity": 1, "message": "299", "line": 15, "column": 3, "nodeType": null, "messageId": "293", "endLine": 15, "endColumn": 9}, {"ruleId": "291", "severity": 1, "message": "300", "line": 27, "column": 5, "nodeType": null, "messageId": "293", "endLine": 27, "endColumn": 20}, {"ruleId": "280", "severity": 2, "message": "281", "line": 47, "column": 32, "nodeType": "282", "messageId": "283", "endLine": 47, "endColumn": 35, "suggestions": "301"}, {"ruleId": "291", "severity": 1, "message": "292", "line": 5, "column": 10, "nodeType": null, "messageId": "293", "endLine": 5, "endColumn": 16}, {"ruleId": "295", "severity": 1, "message": "296", "line": 189, "column": 17, "nodeType": "290", "endLine": 193, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "302", "line": 6, "column": 10, "nodeType": null, "messageId": "293", "endLine": 6, "endColumn": 15}, {"ruleId": "280", "severity": 2, "message": "281", "line": 69, "column": 56, "nodeType": "282", "messageId": "283", "endLine": 69, "endColumn": 59, "suggestions": "303"}, {"ruleId": "291", "severity": 1, "message": "304", "line": 16, "column": 3, "nodeType": null, "messageId": "293", "endLine": 16, "endColumn": 7}, {"ruleId": "291", "severity": 1, "message": "305", "line": 17, "column": 3, "nodeType": null, "messageId": "293", "endLine": 17, "endColumn": 9}, {"ruleId": "291", "severity": 1, "message": "306", "line": 19, "column": 10, "nodeType": null, "messageId": "293", "endLine": 19, "endColumn": 12}, {"ruleId": "280", "severity": 2, "message": "281", "line": 69, "column": 54, "nodeType": "282", "messageId": "283", "endLine": 69, "endColumn": 57, "suggestions": "307"}, {"ruleId": "291", "severity": 1, "message": "308", "line": 6, "column": 10, "nodeType": null, "messageId": "293", "endLine": 6, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "309", "line": 14, "column": 3, "nodeType": null, "messageId": "293", "endLine": 14, "endColumn": 9}, {"ruleId": "280", "severity": 2, "message": "281", "line": 46, "column": 46, "nodeType": "282", "messageId": "283", "endLine": 46, "endColumn": 49, "suggestions": "310"}, {"ruleId": "291", "severity": 1, "message": "311", "line": 104, "column": 16, "nodeType": null, "messageId": "293", "endLine": 104, "endColumn": 21}, {"ruleId": "295", "severity": 1, "message": "296", "line": 264, "column": 23, "nodeType": "290", "endLine": 268, "endColumn": 25}, {"ruleId": "291", "severity": 1, "message": "312", "line": 11, "column": 10, "nodeType": null, "messageId": "293", "endLine": 11, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "313", "line": 13, "column": 18, "nodeType": null, "messageId": "293", "endLine": 13, "endColumn": 19}, {"ruleId": "280", "severity": 2, "message": "281", "line": 53, "column": 30, "nodeType": "282", "messageId": "283", "endLine": 53, "endColumn": 33, "suggestions": "314"}, {"ruleId": "291", "severity": 1, "message": "306", "line": 21, "column": 10, "nodeType": null, "messageId": "293", "endLine": 21, "endColumn": 12}, {"ruleId": "280", "severity": 2, "message": "281", "line": 83, "column": 40, "nodeType": "282", "messageId": "283", "endLine": 83, "endColumn": 43, "suggestions": "315"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 111, "column": 46, "nodeType": "282", "messageId": "283", "endLine": 111, "endColumn": 49, "suggestions": "316"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 231, "column": 19, "nodeType": "290", "endLine": 235, "endColumn": 21}, {"ruleId": "291", "severity": 1, "message": "317", "line": 3, "column": 10, "nodeType": null, "messageId": "293", "endLine": 3, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "318", "line": 3, "column": 31, "nodeType": null, "messageId": "293", "endLine": 3, "endColumn": 37}, {"ruleId": "319", "severity": 2, "message": "320", "line": 4, "column": 18, "nodeType": "321", "messageId": "322", "endLine": 4, "endColumn": 28, "suggestions": "323"}, {"ruleId": "319", "severity": 2, "message": "320", "line": 4, "column": 18, "nodeType": "321", "messageId": "322", "endLine": 4, "endColumn": 31, "suggestions": "324"}, {"ruleId": "291", "severity": 1, "message": "325", "line": 1, "column": 39, "nodeType": null, "messageId": "293", "endLine": 1, "endColumn": 66}, {"ruleId": "291", "severity": 1, "message": "326", "line": 2, "column": 33, "nodeType": null, "messageId": "293", "endLine": 2, "endColumn": 39}, {"ruleId": "291", "severity": 1, "message": "327", "line": 2, "column": 41, "nodeType": null, "messageId": "293", "endLine": 2, "endColumn": 48}, {"ruleId": "291", "severity": 1, "message": "328", "line": 2, "column": 50, "nodeType": null, "messageId": "293", "endLine": 2, "endColumn": 58}, {"ruleId": "280", "severity": 2, "message": "281", "line": 110, "column": 62, "nodeType": "282", "messageId": "283", "endLine": 110, "endColumn": 65, "suggestions": "329"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 127, "column": 55, "nodeType": "282", "messageId": "283", "endLine": 127, "endColumn": 58, "suggestions": "330"}, {"ruleId": "331", "severity": 2, "message": "332", "line": 49, "column": 7, "nodeType": "321", "messageId": "333", "endLine": 49, "endColumn": 15, "fix": "334"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 259, "column": 29, "nodeType": "282", "messageId": "283", "endLine": 259, "endColumn": 32, "suggestions": "335"}, {"ruleId": "280", "severity": 2, "message": "281", "line": 283, "column": 29, "nodeType": "282", "messageId": "283", "endLine": 283, "endColumn": 32, "suggestions": "336"}, {"ruleId": "291", "severity": 1, "message": "337", "line": 4, "column": 10, "nodeType": null, "messageId": "293", "endLine": 4, "endColumn": 14}, {"ruleId": "291", "severity": 1, "message": "338", "line": 4, "column": 16, "nodeType": null, "messageId": "293", "endLine": 4, "endColumn": 27}, {"ruleId": "291", "severity": 1, "message": "339", "line": 4, "column": 29, "nodeType": null, "messageId": "293", "endLine": 4, "endColumn": 39}, {"ruleId": "291", "severity": 1, "message": "340", "line": 4, "column": 41, "nodeType": null, "messageId": "293", "endLine": 4, "endColumn": 50}, {"ruleId": "291", "severity": 1, "message": "302", "line": 6, "column": 10, "nodeType": null, "messageId": "293", "endLine": 6, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "341", "line": 7, "column": 10, "nodeType": null, "messageId": "293", "endLine": 7, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "342", "line": 9, "column": 10, "nodeType": null, "messageId": "293", "endLine": 9, "endColumn": 14}, {"ruleId": "291", "severity": 1, "message": "343", "line": 9, "column": 16, "nodeType": null, "messageId": "293", "endLine": 9, "endColumn": 27}, {"ruleId": "291", "severity": 1, "message": "344", "line": 9, "column": 29, "nodeType": null, "messageId": "293", "endLine": 9, "endColumn": 37}, {"ruleId": "291", "severity": 1, "message": "345", "line": 9, "column": 39, "nodeType": null, "messageId": "293", "endLine": 9, "endColumn": 50}, {"ruleId": "291", "severity": 1, "message": "346", "line": 12, "column": 3, "nodeType": null, "messageId": "293", "endLine": 12, "endColumn": 7}, {"ruleId": "291", "severity": 1, "message": "347", "line": 14, "column": 3, "nodeType": null, "messageId": "293", "endLine": 14, "endColumn": 9}, {"ruleId": "291", "severity": 1, "message": "348", "line": 16, "column": 3, "nodeType": null, "messageId": "293", "endLine": 16, "endColumn": 11}, {"ruleId": "291", "severity": 1, "message": "349", "line": 17, "column": 3, "nodeType": null, "messageId": "293", "endLine": 17, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "350", "line": 28, "column": 11, "nodeType": null, "messageId": "293", "endLine": 28, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "351", "line": 129, "column": 29, "nodeType": null, "messageId": "293", "endLine": 129, "endColumn": 34}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["352", "353"], ["354", "355"], ["356", "357"], ["358", "359"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "unusedVar", "'assetService' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'ImageIcon' is defined but never used.", "'Lock' is defined but never used.", "'Unlock' is defined but never used.", "'setLayerOpacity' is assigned a value but never used.", ["360", "361"], "'Input' is defined but never used.", ["362", "363"], "'Bold' is defined but never used.", "'Italic' is defined but never used.", "'cn' is defined but never used.", ["364", "365"], "'TextLayer' is defined but never used.", "'Share2' is defined but never used.", ["366", "367"], "'error' is defined but never used.", "'Badge' is defined but never used.", "'X' is defined but never used.", ["368", "369"], ["370", "371"], ["372", "373"], "'Check' is defined but never used.", "'Circle' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["374"], ["375"], "'createServerComponentClient' is defined but never used.", "'Rating' is defined but never used.", "'Comment' is defined but never used.", "'Favorite' is defined but never used.", ["376", "377"], ["378", "379"], "prefer-const", "'response' is never reassigned. Use 'const' instead.", "useConst", {"range": "380", "text": "381"}, ["382", "383"], ["384", "385"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Label' is defined but never used.", "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'Type' is defined but never used.", "'Upload' is defined but never used.", "'Sparkles' is defined but never used.", "'ChevronRight' is defined but never used.", "'tool' is assigned a value but never used.", "'index' is defined but never used.", {"messageId": "386", "fix": "387", "desc": "388"}, {"messageId": "389", "fix": "390", "desc": "391"}, {"messageId": "386", "fix": "392", "desc": "388"}, {"messageId": "389", "fix": "393", "desc": "391"}, {"messageId": "386", "fix": "394", "desc": "388"}, {"messageId": "389", "fix": "395", "desc": "391"}, {"messageId": "386", "fix": "396", "desc": "388"}, {"messageId": "389", "fix": "397", "desc": "391"}, {"messageId": "386", "fix": "398", "desc": "388"}, {"messageId": "389", "fix": "399", "desc": "391"}, {"messageId": "386", "fix": "400", "desc": "388"}, {"messageId": "389", "fix": "401", "desc": "391"}, {"messageId": "386", "fix": "402", "desc": "388"}, {"messageId": "389", "fix": "403", "desc": "391"}, {"messageId": "386", "fix": "404", "desc": "388"}, {"messageId": "389", "fix": "405", "desc": "391"}, {"messageId": "386", "fix": "406", "desc": "388"}, {"messageId": "389", "fix": "407", "desc": "391"}, {"messageId": "386", "fix": "408", "desc": "388"}, {"messageId": "389", "fix": "409", "desc": "391"}, {"messageId": "386", "fix": "410", "desc": "388"}, {"messageId": "389", "fix": "411", "desc": "391"}, {"messageId": "412", "fix": "413", "desc": "414"}, {"messageId": "412", "fix": "415", "desc": "414"}, {"messageId": "386", "fix": "416", "desc": "388"}, {"messageId": "389", "fix": "417", "desc": "391"}, {"messageId": "386", "fix": "418", "desc": "388"}, {"messageId": "389", "fix": "419", "desc": "391"}, [1506, 1599], "const response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })", {"messageId": "386", "fix": "420", "desc": "388"}, {"messageId": "389", "fix": "421", "desc": "391"}, {"messageId": "386", "fix": "422", "desc": "388"}, {"messageId": "389", "fix": "423", "desc": "391"}, "suggestUnknown", {"range": "424", "text": "425"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "426", "text": "427"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "428", "text": "425"}, {"range": "429", "text": "427"}, {"range": "430", "text": "425"}, {"range": "431", "text": "427"}, {"range": "432", "text": "425"}, {"range": "433", "text": "427"}, {"range": "434", "text": "425"}, {"range": "435", "text": "427"}, {"range": "436", "text": "425"}, {"range": "437", "text": "427"}, {"range": "438", "text": "425"}, {"range": "439", "text": "427"}, {"range": "440", "text": "425"}, {"range": "441", "text": "427"}, {"range": "442", "text": "425"}, {"range": "443", "text": "427"}, {"range": "444", "text": "425"}, {"range": "445", "text": "427"}, {"range": "446", "text": "425"}, {"range": "447", "text": "427"}, "replaceEmptyInterfaceWithSuper", {"range": "448", "text": "449"}, "Replace empty interface with a type alias.", {"range": "450", "text": "451"}, {"range": "452", "text": "425"}, {"range": "453", "text": "427"}, {"range": "454", "text": "425"}, {"range": "455", "text": "427"}, {"range": "456", "text": "425"}, {"range": "457", "text": "427"}, {"range": "458", "text": "425"}, {"range": "459", "text": "427"}, [998, 1001], "unknown", [998, 1001], "never", [1626, 1629], [1626, 1629], [1570, 1573], [1570, 1573], [1601, 1604], [1601, 1604], [1228, 1231], [1228, 1231], [1999, 2002], [1999, 2002], [1966, 1969], [1966, 1969], [1652, 1655], [1652, 1655], [1637, 1640], [1637, 1640], [2431, 2434], [2431, 2434], [3032, 3035], [3032, 3035], [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [72, 158], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [2707, 2710], [2707, 2710], [3140, 3143], [3140, 3143], [6064, 6067], [6064, 6067], [6664, 6667], [6664, 6667]]