@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Primary colors */
  --primary: #2563eb;
  --primary-foreground: #ffffff;

  /* Secondary colors */
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;

  /* Accent colors */
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;

  /* Destructive colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Muted colors */
  --muted: #f1f5f9;
  --muted-foreground: #64748b;

  /* Border and input colors */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;

  /* Card colors */
  --card: #ffffff;
  --card-foreground: #0f172a;

  /* Popover colors */
  --popover: #ffffff;
  --popover-foreground: #0f172a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Primary colors - darker theme */
    --primary: #3b82f6;
    --primary-foreground: #ffffff;

    /* Secondary colors - darker theme */
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;

    /* Accent colors - darker theme */
    --accent: #1e293b;
    --accent-foreground: #f8fafc;

    /* Destructive colors - darker theme */
    --destructive: #f87171;
    --destructive-foreground: #ffffff;

    /* Muted colors - darker theme */
    --muted: #1e293b;
    --muted-foreground: #94a3b8;

    /* Border and input colors - darker theme */
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;

    /* Card colors - darker theme */
    --card: #0f172a;
    --card-foreground: #f8fafc;

    /* Popover colors - darker theme */
    --popover: #0f172a;
    --popover-foreground: #f8fafc;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
