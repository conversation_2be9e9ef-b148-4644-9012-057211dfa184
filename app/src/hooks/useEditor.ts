'use client'

import { createContext, useContext, useReducer, useCallback, createElement } from 'react'
import { EditorState, EditorTool, CanvasData, ObjectLayer, TextLayer } from '@/types'
import { generateId } from '@/lib/utils'

interface EditorContextType extends EditorState {
  setTool: (tool: EditorTool) => void
  selectObject: (id: string | null) => void
  addTextLayer: (text: string, x: number, y: number) => void
  addObjectLayer: (src: string, x: number, y: number) => void
  updateTextLayer: (id: string, updates: Partial<TextLayer>) => void
  updateObjectLayer: (id: string, updates: Partial<ObjectLayer>) => void
  deleteLayer: (id: string) => void
  setBackground: (background: CanvasData['background']) => void
  moveLayer: (id: string, x: number, y: number) => void
  resizeLayer: (id: string, width: number, height: number) => void
  rotateLayer: (id: string, rotation: number) => void
  setLayerOpacity: (id: string, opacity: number) => void
  bringToFront: (id: string) => void
  sendToBack: (id: string) => void
  undo: () => void
  redo: () => void
  clearCanvas: () => void
  loadCanvasData: (data: CanvasData) => void
}

type EditorAction =
  | { type: 'SET_TOOL'; payload: EditorTool }
  | { type: 'SELECT_OBJECT'; payload: string | null }
  | { type: 'ADD_TEXT_LAYER'; payload: { text: string; x: number; y: number } }
  | { type: 'ADD_OBJECT_LAYER'; payload: { src: string; x: number; y: number } }
  | { type: 'UPDATE_TEXT_LAYER'; payload: { id: string; updates: Partial<TextLayer> } }
  | { type: 'UPDATE_OBJECT_LAYER'; payload: { id: string; updates: Partial<ObjectLayer> } }
  | { type: 'DELETE_LAYER'; payload: string }
  | { type: 'SET_BACKGROUND'; payload: CanvasData['background'] }
  | { type: 'MOVE_LAYER'; payload: { id: string; x: number; y: number } }
  | { type: 'RESIZE_LAYER'; payload: { id: string; width: number; height: number } }
  | { type: 'ROTATE_LAYER'; payload: { id: string; rotation: number } }
  | { type: 'SET_LAYER_OPACITY'; payload: { id: string; opacity: number } }
  | { type: 'BRING_TO_FRONT'; payload: string }
  | { type: 'SEND_TO_BACK'; payload: string }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'CLEAR_CANVAS' }
  | { type: 'LOAD_CANVAS_DATA'; payload: CanvasData }
  | { type: 'SET_LOADING'; payload: boolean }

const initialCanvasData: CanvasData = {
  width: 800,
  height: 600,
  background: {
    type: 'gradient',
    value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  objects: [],
  texts: [
    {
      id: 'default-text-1',
      type: 'text',
      content: '早上一聲好',
      x: 350,
      y: 100,
      fontSize: 48,
      fontFamily: 'Microsoft JhengHei',
      fontWeight: 'bold',
      color: '#FFD700',
      textAlign: 'center',
      rotation: 0,
      opacity: 1,
      zIndex: 2,
      stroke: '#000000',
      strokeWidth: 2
    },
    {
      id: 'default-text-2',
      type: 'text',
      content: '事事皆美好',
      x: 350,
      y: 180,
      fontSize: 48,
      fontFamily: 'Microsoft JhengHei',
      fontWeight: 'bold',
      color: '#FFD700',
      textAlign: 'center',
      rotation: 0,
      opacity: 1,
      zIndex: 3,
      stroke: '#000000',
      strokeWidth: 2
    },
    {
      id: 'default-text-3',
      type: 'text',
      content: '祝您有美好的一天！',
      x: 400,
      y: 450,
      fontSize: 24,
      fontFamily: 'Microsoft JhengHei',
      fontWeight: 'normal',
      color: '#FFFFFF',
      textAlign: 'center',
      rotation: 0,
      opacity: 1,
      zIndex: 4
    }
  ]
}

const initialState: EditorState = {
  tool: 'text',
  selectedObjectId: null,
  canvasData: initialCanvasData,
  history: [initialCanvasData],
  historyIndex: 0,
  isLoading: false
}

function editorReducer(state: EditorState, action: EditorAction): EditorState {
  const saveToHistory = (newCanvasData: CanvasData) => {
    const newHistory = state.history.slice(0, state.historyIndex + 1)
    newHistory.push(newCanvasData)
    return {
      history: newHistory.slice(-20), // Keep only last 20 states
      historyIndex: Math.min(newHistory.length - 1, 19)
    }
  }

  switch (action.type) {
    case 'SET_TOOL':
      return { ...state, tool: action.payload }

    case 'SELECT_OBJECT':
      return { ...state, selectedObjectId: action.payload }

    case 'ADD_TEXT_LAYER': {
      const newText: TextLayer = {
        id: generateId(),
        type: 'text',
        content: action.payload.text,
        x: action.payload.x,
        y: action.payload.y,
        fontSize: 24,
        fontFamily: 'Arial',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'left',
        rotation: 0,
        opacity: 1,
        zIndex: Math.max(...state.canvasData.texts.map(t => t.zIndex), 0) + 1
      }
      const newCanvasData = {
        ...state.canvasData,
        texts: [...state.canvasData.texts, newText]
      }
      return {
        ...state,
        canvasData: newCanvasData,
        selectedObjectId: newText.id,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'ADD_OBJECT_LAYER': {
      const newObject: ObjectLayer = {
        id: generateId(),
        type: 'image',
        src: action.payload.src,
        x: action.payload.x,
        y: action.payload.y,
        width: 200,
        height: 200,
        rotation: 0,
        opacity: 1,
        flipX: false,
        flipY: false,
        zIndex: Math.max(...state.canvasData.objects.map(o => o.zIndex), 0) + 1
      }
      const newCanvasData = {
        ...state.canvasData,
        objects: [...state.canvasData.objects, newObject]
      }
      return {
        ...state,
        canvasData: newCanvasData,
        selectedObjectId: newObject.id,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'UPDATE_TEXT_LAYER': {
      const newCanvasData = {
        ...state.canvasData,
        texts: state.canvasData.texts.map(text =>
          text.id === action.payload.id
            ? { ...text, ...action.payload.updates }
            : text
        )
      }
      return {
        ...state,
        canvasData: newCanvasData,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'UPDATE_OBJECT_LAYER': {
      const newCanvasData = {
        ...state.canvasData,
        objects: state.canvasData.objects.map(obj =>
          obj.id === action.payload.id
            ? { ...obj, ...action.payload.updates }
            : obj
        )
      }
      return {
        ...state,
        canvasData: newCanvasData,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'DELETE_LAYER': {
      const newCanvasData = {
        ...state.canvasData,
        texts: state.canvasData.texts.filter(text => text.id !== action.payload),
        objects: state.canvasData.objects.filter(obj => obj.id !== action.payload)
      }
      return {
        ...state,
        canvasData: newCanvasData,
        selectedObjectId: state.selectedObjectId === action.payload ? null : state.selectedObjectId,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'SET_BACKGROUND': {
      const newCanvasData = {
        ...state.canvasData,
        background: action.payload
      }
      return {
        ...state,
        canvasData: newCanvasData,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'UNDO':
      if (state.historyIndex > 0) {
        const newIndex = state.historyIndex - 1
        return {
          ...state,
          canvasData: state.history[newIndex],
          historyIndex: newIndex,
          selectedObjectId: null
        }
      }
      return state

    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        const newIndex = state.historyIndex + 1
        return {
          ...state,
          canvasData: state.history[newIndex],
          historyIndex: newIndex,
          selectedObjectId: null
        }
      }
      return state

    case 'CLEAR_CANVAS': {
      const newCanvasData = { ...initialCanvasData }
      return {
        ...state,
        canvasData: newCanvasData,
        selectedObjectId: null,
        ...saveToHistory(newCanvasData)
      }
    }

    case 'LOAD_CANVAS_DATA':
      return {
        ...state,
        canvasData: action.payload,
        selectedObjectId: null,
        ...saveToHistory(action.payload)
      }

    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }

    default:
      return state
  }
}

const EditorContext = createContext<EditorContextType | undefined>(undefined)

export function EditorProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(editorReducer, initialState)

  const setTool = useCallback((tool: EditorTool) => {
    dispatch({ type: 'SET_TOOL', payload: tool })
  }, [])

  const selectObject = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_OBJECT', payload: id })
  }, [])

  const addTextLayer = useCallback((text: string, x: number, y: number) => {
    dispatch({ type: 'ADD_TEXT_LAYER', payload: { text, x, y } })
  }, [])

  const addObjectLayer = useCallback((src: string, x: number, y: number) => {
    dispatch({ type: 'ADD_OBJECT_LAYER', payload: { src, x, y } })
  }, [])

  const updateTextLayer = useCallback((id: string, updates: Partial<TextLayer>) => {
    dispatch({ type: 'UPDATE_TEXT_LAYER', payload: { id, updates } })
  }, [])

  const updateObjectLayer = useCallback((id: string, updates: Partial<ObjectLayer>) => {
    dispatch({ type: 'UPDATE_OBJECT_LAYER', payload: { id, updates } })
  }, [])

  const deleteLayer = useCallback((id: string) => {
    dispatch({ type: 'DELETE_LAYER', payload: id })
  }, [])

  const setBackground = useCallback((background: CanvasData['background']) => {
    dispatch({ type: 'SET_BACKGROUND', payload: background })
  }, [])

  const moveLayer = useCallback((id: string, x: number, y: number) => {
    const isText = state.canvasData.texts.some(t => t.id === id)
    if (isText) {
      updateTextLayer(id, { x, y })
    } else {
      updateObjectLayer(id, { x, y })
    }
  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])

  const resizeLayer = useCallback((id: string, width: number, height: number) => {
    updateObjectLayer(id, { width, height })
  }, [updateObjectLayer])

  const rotateLayer = useCallback((id: string, rotation: number) => {
    const isText = state.canvasData.texts.some(t => t.id === id)
    if (isText) {
      updateTextLayer(id, { rotation })
    } else {
      updateObjectLayer(id, { rotation })
    }
  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])

  const setLayerOpacity = useCallback((id: string, opacity: number) => {
    const isText = state.canvasData.texts.some(t => t.id === id)
    if (isText) {
      updateTextLayer(id, { opacity })
    } else {
      updateObjectLayer(id, { opacity })
    }
  }, [state.canvasData.texts, updateTextLayer, updateObjectLayer])

  const bringToFront = useCallback((id: string) => {
    const maxZ = Math.max(
      ...state.canvasData.texts.map(t => t.zIndex),
      ...state.canvasData.objects.map(o => o.zIndex)
    )
    const isText = state.canvasData.texts.some(t => t.id === id)
    if (isText) {
      updateTextLayer(id, { zIndex: maxZ + 1 })
    } else {
      updateObjectLayer(id, { zIndex: maxZ + 1 })
    }
  }, [state.canvasData, updateTextLayer, updateObjectLayer])

  const sendToBack = useCallback((id: string) => {
    const minZ = Math.min(
      ...state.canvasData.texts.map(t => t.zIndex),
      ...state.canvasData.objects.map(o => o.zIndex)
    )
    const isText = state.canvasData.texts.some(t => t.id === id)
    if (isText) {
      updateTextLayer(id, { zIndex: minZ - 1 })
    } else {
      updateObjectLayer(id, { zIndex: minZ - 1 })
    }
  }, [state.canvasData, updateTextLayer, updateObjectLayer])

  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const clearCanvas = useCallback(() => {
    dispatch({ type: 'CLEAR_CANVAS' })
  }, [])

  const loadCanvasData = useCallback((data: CanvasData) => {
    dispatch({ type: 'LOAD_CANVAS_DATA', payload: data })
  }, [])

  const value = {
    ...state,
    setTool,
    selectObject,
    addTextLayer,
    addObjectLayer,
    updateTextLayer,
    updateObjectLayer,
    deleteLayer,
    setBackground,
    moveLayer,
    resizeLayer,
    rotateLayer,
    setLayerOpacity,
    bringToFront,
    sendToBack,
    undo,
    redo,
    clearCanvas,
    loadCanvasData
  }

  return createElement(EditorContext.Provider, { value }, children)
}

export function useEditor() {
  const context = useContext(EditorContext)
  if (context === undefined) {
    throw new Error('useEditor must be used within an EditorProvider')
  }
  return context
}
