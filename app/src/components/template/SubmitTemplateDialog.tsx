'use client'

import { useState } from 'react'
import { useEditor } from '@/hooks/useEditor'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { templateService } from '@/lib/templateService'
import { Upload, X } from 'lucide-react'

interface SubmitTemplateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmitted?: (templateId: string) => void
}

export function SubmitTemplateDialog({ open, onOpenChange, onSubmitted }: SubmitTemplateDialogProps) {
  const { canvasData } = useEditor()
  const { user } = useAuth()
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!title.trim()) {
      alert('請輸入樣板標題')
      return
    }

    if (!user) {
      alert('請先登入')
      return
    }

    setSubmitting(true)
    try {
      const template = await templateService.saveTemplate(
        title,
        description,
        canvasData,
        selectedCategories,
        selectedTags
      )

      // 更新為待審核狀態
      await templateService.updateTemplate(template.id, {
        status: 'pending' as any,
        is_public: true
      })

      onSubmitted?.(template.id)
      onOpenChange(false)
      alert('樣板已提交審核，審核通過後將會公開！')
      
      // 重置表單
      setTitle('')
      setDescription('')
      setSelectedCategories([])
      setSelectedTags([])
    } catch (error) {
      console.error('Failed to submit template:', error)
      alert('提交失敗，請稍後再試')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            投稿樣板
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">樣板標題 *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="為您的樣板取個好名字"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="description">樣板描述</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="簡單描述這個樣板的特色和用途"
                className="mt-1"
                rows={3}
              />
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2 text-blue-900">投稿須知</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 樣板內容需健康正向，不得包含不當內容</li>
              <li>• 確保使用的圖片和文字沒有版權問題</li>
              <li>• 樣板將經過審核，審核通過後才會公開</li>
              <li>• 優質樣板將有機會被推薦到首頁</li>
            </ul>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={submitting}
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !title.trim()}
            >
              {submitting ? '提交中...' : '提交審核'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
