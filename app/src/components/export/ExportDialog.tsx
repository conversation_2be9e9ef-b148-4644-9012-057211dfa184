'use client'

import { useState, useEffect } from 'react'
import { useEditor } from '@/hooks/useEditor'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ExportService, ExportOptions } from '@/lib/exportService'
import { 
  Download, 
  Share2, 
  Copy, 
  Eye,
  Facebook,
  Twitter,
  MessageCircle,
  Link
} from 'lucide-react'

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const EXPORT_PRESETS = [
  { name: '原始尺寸', width: 800, height: 600 },
  { name: 'Instagram 正方形', width: 1080, height: 1080 },
  { name: 'Facebook 封面', width: 1200, height: 630 },
  { name: 'Twitter 標頭', width: 1500, height: 500 },
  { name: 'LINE 貼圖', width: 370, height: 320 },
  { name: '手機桌布', width: 1080, height: 1920 }
]

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const { canvasData } = useEditor()
  const [format, setFormat] = useState<'png' | 'jpeg'>('png')
  const [quality, setQuality] = useState(90)
  const [width, setWidth] = useState(800)
  const [height, setHeight] = useState(600)
  const [pixelRatio, setPixelRatio] = useState(2)
  const [isExporting, setIsExporting] = useState(false)
  const [exportedImage, setExportedImage] = useState<string | null>(null)
  const [imageInfo, setImageInfo] = useState<any>(null)

  // 重置為原始尺寸
  useEffect(() => {
    if (open) {
      setWidth(canvasData.width)
      setHeight(canvasData.height)
      setExportedImage(null)
      setImageInfo(null)
    }
  }, [open, canvasData.width, canvasData.height])

  // 套用預設尺寸
  const applyPreset = (preset: typeof EXPORT_PRESETS[0]) => {
    setWidth(preset.width)
    setHeight(preset.height)
  }

  // 匯出圖片
  const handleExport = async () => {
    setIsExporting(true)
    try {
      const options: ExportOptions = {
        format,
        quality: format === 'jpeg' ? quality / 100 : undefined,
        width,
        height,
        pixelRatio
      }

      const dataURL = await ExportService.exportCanvas(canvasData, options)
      setExportedImage(dataURL)

      // 獲取圖片資訊
      const info = await ExportService.getImageInfo(dataURL)
      setImageInfo(info)
    } catch (error) {
      console.error('Export failed:', error)
      alert('匯出失敗，請稍後再試')
    } finally {
      setIsExporting(false)
    }
  }

  // 下載圖片
  const handleDownload = () => {
    if (exportedImage) {
      const filename = `meme-${Date.now()}.${format}`
      ExportService.downloadImage(exportedImage, filename)
    }
  }

  // 複製到剪貼簿
  const handleCopyToClipboard = async () => {
    if (exportedImage) {
      try {
        await ExportService.copyToClipboard(exportedImage)
        alert('已複製到剪貼簿')
      } catch (error) {
        alert('複製失敗，請使用下載功能')
      }
    }
  }

  // 預覽圖片
  const handlePreview = () => {
    if (exportedImage) {
      ExportService.previewImage(exportedImage)
    }
  }

  // 社群分享
  const handleSocialShare = (platform: 'facebook' | 'twitter' | 'line') => {
    ExportService.shareToSocial(platform, exportedImage || undefined, '來看看我用早安圖產生器製作的圖片！')
  }

  // 複製分享連結
  const handleCopyShareLink = () => {
    const shareLink = ExportService.generateShareLink()
    navigator.clipboard.writeText(shareLink)
    alert('分享連結已複製到剪貼簿')
  }

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            匯出與分享
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="export" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export">匯出設定</TabsTrigger>
            <TabsTrigger value="share">分享</TabsTrigger>
          </TabsList>

          {/* 匯出設定 */}
          <TabsContent value="export" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 設定面板 */}
              <div className="space-y-4">
                <h3 className="font-medium">匯出設定</h3>

                {/* 格式選擇 */}
                <div>
                  <Label>檔案格式</Label>
                  <Select value={format} onValueChange={(value: 'png' | 'jpeg') => setFormat(value)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG (透明背景)</SelectItem>
                      <SelectItem value="jpeg">JPEG (較小檔案)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* JPEG 品質 */}
                {format === 'jpeg' && (
                  <div>
                    <Label>品質: {quality}%</Label>
                    <Slider
                      value={[quality]}
                      onValueChange={([value]) => setQuality(value)}
                      min={10}
                      max={100}
                      step={5}
                      className="mt-1"
                    />
                  </div>
                )}

                {/* 尺寸設定 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>寬度 (px)</Label>
                    <input
                      type="number"
                      value={width}
                      onChange={(e) => setWidth(parseInt(e.target.value) || 800)}
                      className="w-full mt-1 px-3 py-2 border rounded-md"
                      min="100"
                      max="4000"
                    />
                  </div>
                  <div>
                    <Label>高度 (px)</Label>
                    <input
                      type="number"
                      value={height}
                      onChange={(e) => setHeight(parseInt(e.target.value) || 600)}
                      className="w-full mt-1 px-3 py-2 border rounded-md"
                      min="100"
                      max="4000"
                    />
                  </div>
                </div>

                {/* 預設尺寸 */}
                <div>
                  <Label>快速尺寸</Label>
                  <div className="grid grid-cols-2 gap-2 mt-1">
                    {EXPORT_PRESETS.map((preset) => (
                      <Button
                        key={preset.name}
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset(preset)}
                        className="text-xs"
                      >
                        {preset.name}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 解析度 */}
                <div>
                  <Label>解析度倍數: {pixelRatio}x</Label>
                  <Slider
                    value={[pixelRatio]}
                    onValueChange={([value]) => setPixelRatio(value)}
                    min={1}
                    max={4}
                    step={0.5}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    更高的倍數會產生更清晰但更大的檔案
                  </p>
                </div>

                {/* 匯出按鈕 */}
                <Button
                  onClick={handleExport}
                  disabled={isExporting}
                  className="w-full"
                >
                  {isExporting ? '匯出中...' : '生成圖片'}
                </Button>
              </div>

              {/* 預覽面板 */}
              <div className="space-y-4">
                <h3 className="font-medium">預覽</h3>
                
                {exportedImage ? (
                  <div className="space-y-4">
                    <div className="border rounded-lg overflow-hidden">
                      <img
                        src={exportedImage}
                        alt="Exported"
                        className="w-full h-auto max-h-64 object-contain bg-gray-50"
                      />
                    </div>

                    {/* 圖片資訊 */}
                    {imageInfo && (
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>尺寸: {imageInfo.width} x {imageInfo.height}</p>
                        <p>格式: {imageInfo.format.toUpperCase()}</p>
                        <p>檔案大小: {formatFileSize(imageInfo.size)}</p>
                      </div>
                    )}

                    {/* 操作按鈕 */}
                    <div className="flex gap-2">
                      <Button onClick={handleDownload} className="flex-1">
                        <Download className="h-4 w-4 mr-2" />
                        下載
                      </Button>
                      <Button variant="outline" onClick={handleCopyToClipboard}>
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" onClick={handlePreview}>
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
                    點擊「生成圖片」來預覽結果
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* 分享設定 */}
          <TabsContent value="share" className="space-y-6">
            <div className="space-y-4">
              <h3 className="font-medium">分享到社群媒體</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  onClick={() => handleSocialShare('facebook')}
                  className="flex items-center gap-2"
                >
                  <Facebook className="h-4 w-4 text-blue-600" />
                  Facebook
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => handleSocialShare('twitter')}
                  className="flex items-center gap-2"
                >
                  <Twitter className="h-4 w-4 text-blue-400" />
                  Twitter
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => handleSocialShare('line')}
                  className="flex items-center gap-2"
                >
                  <MessageCircle className="h-4 w-4 text-green-500" />
                  LINE
                </Button>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">分享連結</h4>
                <Button
                  variant="outline"
                  onClick={handleCopyShareLink}
                  className="flex items-center gap-2"
                >
                  <Link className="h-4 w-4" />
                  複製分享連結
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
