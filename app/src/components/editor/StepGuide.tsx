'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ImageIcon,
  Type,
  MessageSquare,
  Upload,
  Palette,
  Sparkles,
  ChevronRight
} from 'lucide-react'
import { useEditor } from '@/hooks/useEditor'
import { TextPanel } from './TextPanel'
import { TextPresets } from './TextPresets'
import { BackgroundPanel } from './BackgroundPanel'
import { BackgroundLibrary } from './BackgroundLibrary'
import { ObjectPanel } from './ObjectPanel'

// 示範物件數據 - 與 ObjectLibrary 中的相同
const DEMO_OBJECTS = [
  {
    id: '1',
    name: '櫻花',
    url: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?w=200&h=200&fit=crop',
    category: 'flowers',
    tags: ['櫻花', '花朵', '粉色', '春天']
  },
  {
    id: '2',
    name: '愛心',
    url: 'https://images.unsplash.com/photo-1518199266791-5375a83190b7?w=200&h=200&fit=crop',
    category: 'shapes',
    tags: ['愛心', '形狀', '紅色', '愛情']
  },
  {
    id: '3',
    name: '蝴蝶',
    url: 'https://images.unsplash.com/photo-1444927714506-8492d94b5ba0?w=200&h=200&fit=crop',
    category: 'animals',
    tags: ['蝴蝶', '動物', '昆蟲', '彩色']
  },
  {
    id: '4',
    name: '星星',
    url: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=200&h=200&fit=crop',
    category: 'shapes',
    tags: ['星星', '形狀', '夜空', '閃亮']
  },
  {
    id: '5',
    name: '太陽花',
    url: 'https://images.unsplash.com/photo-1470509037663-253afd7f0f51?w=200&h=200&fit=crop',
    category: 'flowers',
    tags: ['太陽花', '花朵', '黃色', '夏天']
  },
  {
    id: '6',
    name: '禮物盒',
    url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=200&h=200&fit=crop',
    category: 'decorations',
    tags: ['禮物', '裝飾', '慶祝', '驚喜']
  },
  {
    id: '7',
    name: '小鳥',
    url: 'https://images.unsplash.com/photo-1444464666168-49d633b86797?w=200&h=200&fit=crop',
    category: 'animals',
    tags: ['小鳥', '動物', '飛行', '自由']
  },
  {
    id: '8',
    name: '雲朵',
    url: 'https://images.unsplash.com/photo-1419833479618-c595710ac50c?w=200&h=200&fit=crop',
    category: 'shapes',
    tags: ['雲朵', '形狀', '天空', '白色']
  }
]

export function StepGuide() {
  const [activeStep, setActiveStep] = useState(1)
  const { tool, setTool, addTextLayer, addObjectLayer } = useEditor()

  const steps = [
    {
      id: 1,
      title: '物件照片',
      description: '主角可以是任何你喜歡的！無論是你的小孩、寵物狗狗貓貓、心愛的車輛等，我們都能自動幫你去掉背景！當然，不使用任何物件作畫也是一種選擇，我們一樣能為你製作出精彩的早安圖。',
      icon: ImageIcon,
      color: 'bg-blue-500'
    },
    {
      id: 2,
      title: '背景圖',
      description: '我們可以自動為你生成背景圖，或者你也可以選擇自行上傳圖片。',
      icon: Palette,
      color: 'bg-green-500'
    },
    {
      id: 3,
      title: '問候語',
      description: '選擇一個你喜愛的主題，我們自動幫你生成或問候語！你也可以選擇自行輸入你想使用的問候語。',
      icon: MessageSquare,
      color: 'bg-purple-500'
    }
  ]

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId)
    // Set appropriate tool based on step
    switch (stepId) {
      case 1:
        setTool('select') // 暫時使用 select，因為 object 工具可能需要特殊處理
        break
      case 2:
        setTool('background')
        break
      case 3:
        setTool('text')
        break
    }
  }

  // 處理按鈕點擊事件
  const handleButtonClick = (action: string, step: number) => {
    console.log(`Button clicked: ${action} in step ${step}`)

    switch (step) {
      case 1: // 物件照片步驟
        switch (action) {
          case 'my-library':
            console.log('打開我的圖庫')
            break
          case 'library':
            console.log('打開圖庫')
            break
          case 'upload':
            console.log('自行上傳')
            break
          case 'no-object':
            console.log('無，純背景')
            break
        }
        break
      case 2: // 背景圖步驟
        switch (action) {
          case 'my-library':
            console.log('打開我的背景圖庫')
            break
          case 'library':
            console.log('打開背景圖庫')
            break
          case 'auto-generate':
            console.log('自動生成背景')
            break
          case 'upload':
            console.log('自行上傳背景')
            break
        }
        break
      case 3: // 問候語步驟
        switch (action) {
          case 'auto-generate':
            console.log('自動生成問候語')
            // 添加示例文字到畫布
            addTextLayer('早上一聲好　事事皆美好', 100, 100)
            break
          case 'manual-input':
            console.log('自行輸入問候語')
            break
          case 'preset-1':
            addTextLayer('新的一天開始了，祝你有美好的一天', 100, 100)
            break
          case 'preset-2':
            addTextLayer('早安！願你今天充滿陽光和快樂', 100, 100)
            break
          case 'preset-3':
            addTextLayer('今天也要加油喔！', 100, 100)
            break
        }
        break
    }
  }

  // 處理物件選擇
  const handleObjectSelect = (object: typeof DEMO_OBJECTS[0]) => {
    console.log(`Selected object: ${object.name}`)
    addObjectLayer(object.url, 100, 100)
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 1:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="flex-1 bg-gray-600 text-white hover:bg-gray-700"
                onClick={() => handleButtonClick('my-library', 1)}
              >
                我的圖庫
              </Button>
              <Button
                className="flex-1 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => handleButtonClick('library', 1)}
              >
                圖庫
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleButtonClick('upload', 1)}
              >
                自行上傳
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleButtonClick('no-object', 1)}
              >
                無，純背景
              </Button>
            </div>

            {/* 圖庫 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">圖庫</h4>
              <div className="grid grid-cols-4 gap-2">
                {DEMO_OBJECTS.map((object) => (
                  <div
                    key={object.id}
                    className="aspect-square bg-gray-100 rounded border hover:border-blue-300 cursor-pointer relative overflow-hidden"
                    onClick={() => handleObjectSelect(object)}
                  >
                    <img
                      src={object.url}
                      alt={object.name}
                      className="w-full h-full object-cover hover:scale-105 transition-transform"
                      onError={(e) => {
                        // 如果圖片載入失敗，顯示佔位符
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const parent = target.parentElement
                        if (parent) {
                          const placeholder = document.createElement('div')
                          placeholder.className = 'w-full h-full flex items-center justify-center text-gray-400'
                          placeholder.innerHTML = `<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>`
                          parent.appendChild(placeholder)
                        }
                      }}
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-1">
                      <p className="text-white text-xs font-medium truncate">
                        {object.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <Button
                variant="ghost"
                className="w-full text-gray-500 hover:text-gray-700"
                onClick={() => console.log('查看更多物件')}
              >
                <ChevronRight className="h-4 w-4 mr-1" />
                查看更多
              </Button>
            </div>
          </div>
        )
      case 2:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="flex-1 bg-gray-600 text-white hover:bg-gray-700"
                onClick={() => handleButtonClick('my-library', 2)}
              >
                我的圖庫
              </Button>
              <Button
                className="flex-1 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => handleButtonClick('library', 2)}
              >
                圖庫
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleButtonClick('auto-generate', 2)}
              >
                自動生成
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleButtonClick('upload', 2)}
              >
                自行上傳
              </Button>
            </div>

            {/* 背景圖庫 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">圖庫</h4>
              <div className="grid grid-cols-3 gap-2">
                {/* Sample background thumbnails */}
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div
                    key={i}
                    className="aspect-video bg-gradient-to-br from-orange-400 to-red-500 rounded border hover:border-blue-300 cursor-pointer relative"
                    onClick={() => console.log(`Selected background ${i}`)}
                  >
                    {/* 示例：添加一些有選中標記的背景 */}
                    {i === 1 && (
                      <div className="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                    {/* 標記為火熱背景 */}
                    {[1, 2, 3].includes(i) && (
                      <div className="absolute top-1 left-1 bg-red-500 text-white text-xs px-1 rounded">
                        火熱背景
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <Button
                variant="ghost"
                className="w-full text-gray-500 hover:text-gray-700"
                onClick={() => console.log('查看更多背景')}
              >
                <ChevronRight className="h-4 w-4 mr-1" />
                查看更多
              </Button>
            </div>
          </div>
        )
      case 3:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button
                className="flex-1 bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => handleButtonClick('auto-generate', 3)}
              >
                自動生成
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleButtonClick('manual-input', 3)}
              >
                自行輸入
              </Button>
            </div>

            {/* 問候語選項 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">問候語</h4>
              <div className="space-y-2">
                <div className="p-3 bg-gray-50 rounded border text-center">
                  <div className="font-medium text-lg">早上一聲好　事事皆美好</div>
                </div>
                <Button
                  variant="outline"
                  className="w-full justify-center py-6"
                  onClick={() => handleButtonClick('preset-1', 3)}
                >
                  新的一天開始了，祝你有美好的一天
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-center py-6"
                  onClick={() => handleButtonClick('preset-2', 3)}
                >
                  早安！願你今天充滿陽光和快樂
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-center py-6"
                  onClick={() => handleButtonClick('preset-3', 3)}
                >
                  今天也要加油喔！
                </Button>
              </div>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Step Navigation */}
      <div className="p-4 border-b border-gray-200">
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start gap-3">
              {/* Step Number */}
              <div className={`
                flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium
                ${activeStep === step.id ? step.color : 'bg-gray-300'}
                ${activeStep > step.id ? 'bg-green-500' : ''}
                cursor-pointer transition-colors
              `}
              onClick={() => handleStepClick(step.id)}
              >
                {activeStep > step.id ? '✓' : step.id}
              </div>
              
              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <step.icon className="h-4 w-4 text-gray-600" />
                  <h3 className={`font-medium text-sm ${activeStep === step.id ? 'text-blue-600' : 'text-gray-900'}`}>
                    {step.title}
                  </h3>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="secondary" className="text-xs">
              Step {activeStep}
            </Badge>
            <h2 className="font-medium text-lg">
              {steps.find(s => s.id === activeStep)?.title}
            </h2>
          </div>
        </div>
        
        {renderStepContent()}
      </div>
    </div>
  )
}
