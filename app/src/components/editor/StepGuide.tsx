'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ImageIcon, 
  Type, 
  MessageSquare, 
  Upload,
  Palette,
  Sparkles,
  ChevronRight
} from 'lucide-react'
import { useEditor } from '@/hooks/useEditor'
import { TextPanel } from './TextPanel'
import { TextPresets } from './TextPresets'
import { BackgroundPanel } from './BackgroundPanel'
import { BackgroundLibrary } from './BackgroundLibrary'
import { ObjectPanel } from './ObjectPanel'

export function StepGuide() {
  const [activeStep, setActiveStep] = useState(1)
  const { tool, setTool } = useEditor()

  const steps = [
    {
      id: 1,
      title: '物件照片',
      description: '主角可以是任何你喜歡的！無論是你的小孩、寵物狗狗貓貓、心愛的車輛等，我們都能自動幫你去掉背景！當然，不使用任何物件作畫也是一種選擇，我們一樣能為你製作出精彩的早安圖。',
      icon: ImageIcon,
      color: 'bg-blue-500'
    },
    {
      id: 2,
      title: '背景圖',
      description: '我們可以自動為你生成背景圖，或者你也可以選擇自行上傳圖片。',
      icon: Palette,
      color: 'bg-green-500'
    },
    {
      id: 3,
      title: '問候語',
      description: '選擇一個你喜愛的主題，我們自動幫你生成或問候語！你也可以選擇自行輸入你想使用的問候語。',
      icon: MessageSquare,
      color: 'bg-purple-500'
    }
  ]

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId)
    // Set appropriate tool based on step
    switch (stepId) {
      case 1:
        setTool('select') // 暫時使用 select，因為 object 工具可能需要特殊處理
        break
      case 2:
        setTool('background')
        break
      case 3:
        setTool('text')
        break
    }
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 1:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button variant="outline" className="flex-1 bg-gray-600 text-white hover:bg-gray-700">
                我的圖庫
              </Button>
              <Button className="flex-1 bg-blue-600 text-white hover:bg-blue-700">
                圖庫
              </Button>
              <Button variant="outline" className="flex-1">
                自行上傳
              </Button>
              <Button variant="outline" className="flex-1">
                無，純背景
              </Button>
            </div>

            {/* 圖庫 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">圖庫</h4>
              <div className="grid grid-cols-4 gap-2">
                {/* Sample object thumbnails */}
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <div key={i} className="aspect-square bg-gray-100 rounded border hover:border-blue-300 cursor-pointer relative">
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <ImageIcon className="h-6 w-6" />
                    </div>
                    {/* 示例：添加一些有選中標記的圖片 */}
                    {i === 2 && (
                      <div className="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full text-gray-500 hover:text-gray-700">
                <ChevronRight className="h-4 w-4 mr-1" />
                查看更多
              </Button>
            </div>
          </div>
        )
      case 2:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button variant="outline" className="flex-1 bg-gray-600 text-white hover:bg-gray-700">
                我的圖庫
              </Button>
              <Button className="flex-1 bg-blue-600 text-white hover:bg-blue-700">
                圖庫
              </Button>
              <Button variant="outline" className="flex-1">
                自動生成
              </Button>
              <Button variant="outline" className="flex-1">
                自行上傳
              </Button>
            </div>

            {/* 背景圖庫 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">圖庫</h4>
              <div className="grid grid-cols-3 gap-2">
                {/* Sample background thumbnails */}
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="aspect-video bg-gradient-to-br from-orange-400 to-red-500 rounded border hover:border-blue-300 cursor-pointer relative">
                    {/* 示例：添加一些有選中標記的背景 */}
                    {i === 1 && (
                      <div className="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                    {/* 標記為火熱背景 */}
                    {[1, 2, 3].includes(i) && (
                      <div className="absolute top-1 left-1 bg-red-500 text-white text-xs px-1 rounded">
                        火熱背景
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full text-gray-500 hover:text-gray-700">
                <ChevronRight className="h-4 w-4 mr-1" />
                查看更多
              </Button>
            </div>
          </div>
        )
      case 3:
        return (
          <div className="space-y-4">
            {/* 操作按鈕 */}
            <div className="flex gap-2">
              <Button className="flex-1 bg-blue-600 text-white hover:bg-blue-700">
                自動生成
              </Button>
              <Button variant="outline" className="flex-1">
                自行輸入
              </Button>
            </div>

            {/* 問候語選項 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">問候語</h4>
              <div className="space-y-2">
                <div className="p-3 bg-gray-50 rounded border text-center">
                  <div className="font-medium text-lg">早上一聲好　事事皆美好</div>
                </div>
                <Button variant="outline" className="w-full justify-center py-6">
                  新的一天開始了，祝你有美好的一天
                </Button>
                <Button variant="outline" className="w-full justify-center py-6">
                  早安！願你今天充滿陽光和快樂
                </Button>
                <Button variant="outline" className="w-full justify-center py-6">
                  今天也要加油喔！
                </Button>
              </div>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Step Navigation */}
      <div className="p-4 border-b border-gray-200">
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start gap-3">
              {/* Step Number */}
              <div className={`
                flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium
                ${activeStep === step.id ? step.color : 'bg-gray-300'}
                ${activeStep > step.id ? 'bg-green-500' : ''}
                cursor-pointer transition-colors
              `}
              onClick={() => handleStepClick(step.id)}
              >
                {activeStep > step.id ? '✓' : step.id}
              </div>
              
              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <step.icon className="h-4 w-4 text-gray-600" />
                  <h3 className={`font-medium text-sm ${activeStep === step.id ? 'text-blue-600' : 'text-gray-900'}`}>
                    {step.title}
                  </h3>
                </div>
                <p className="text-xs text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="secondary" className="text-xs">
              Step {activeStep}
            </Badge>
            <h2 className="font-medium text-lg">
              {steps.find(s => s.id === activeStep)?.title}
            </h2>
          </div>
        </div>
        
        {renderStepContent()}
      </div>
    </div>
  )
}
