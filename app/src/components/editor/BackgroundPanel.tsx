'use client'

import { useState, useRef } from 'react'
import { useEditor } from '@/hooks/useEditor'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Palette, 
  Upload, 
  Sparkles,
  Image as ImageIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'

const COLOR_PRESETS = [
  '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da',
  '#adb5bd', '#6c757d', '#495057', '#343a40', '#212529',
  '#ff6b6b', '#ee5a52', '#ff8787', '#ffa8a8', '#ffc9c9',
  '#51cf66', '#40c057', '#69db7c', '#8ce99a', '#b2f2bb',
  '#339af0', '#228be6', '#74c0fc', '#a5d8ff', '#d0ebff',
  '#845ef7', '#7950f2', '#9775fa', '#b197fc', '#d0bfff',
  '#ffd43b', '#fab005', '#ffec99', '#fff3bf', '#fff9db',
  '#fd7e14', '#e8590c', '#ffa94d', '#ffb366', '#ffc078'
]

const GRADIENT_PRESETS = [
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
  'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
]

export function BackgroundPanel() {
  const { tool, canvasData, setBackground } = useEditor()
  const [customColor, setCustomColor] = useState('#ffffff')
  const [aiPrompt, setAiPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleColorChange = (color: string) => {
    setBackground({ type: 'color', value: color })
  }

  const handleCustomColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value
    setCustomColor(color)
    setBackground({ type: 'color', value: color })
  }

  const handleGradientChange = (gradient: string) => {
    setBackground({ type: 'gradient', value: gradient })
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string
        setBackground({ type: 'image', value: imageUrl })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleAiGenerate = async () => {
    if (!aiPrompt.trim()) return
    
    setIsGenerating(true)
    try {
      // TODO: Integrate with AI service
      // For now, we'll use a placeholder
      setTimeout(() => {
        // Placeholder: use a gradient as generated background
        const randomGradient = GRADIENT_PRESETS[0]
        setBackground({ type: 'gradient', value: randomGradient })
        setIsGenerating(false)
      }, 2000)
    } catch (error) {
      console.error('AI generation failed:', error)
      setIsGenerating(false)
    }
  }

  if (tool !== 'background') {
    return null
  }

  return (
    <Card className="w-80">
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <Palette className="h-4 w-4" />
          背景設定
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="color" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="color" className="text-xs">顏色</TabsTrigger>
            <TabsTrigger value="gradient" className="text-xs">漸層</TabsTrigger>
            <TabsTrigger value="upload" className="text-xs">上傳</TabsTrigger>
            <TabsTrigger value="ai" className="text-xs">AI</TabsTrigger>
          </TabsList>

          {/* Solid Colors */}
          <TabsContent value="color" className="space-y-4">
            <div>
              <Label className="text-sm">預設顏色</Label>
              <div className="grid grid-cols-5 gap-2 mt-2">
                {COLOR_PRESETS.map((color) => (
                  <button
                    key={color}
                    className={cn(
                      "w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400 transition-colors",
                      canvasData.background?.type === 'color' && 
                      canvasData.background?.value === color && 
                      "border-blue-500 ring-2 ring-blue-200"
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm">自訂顏色</Label>
              <div className="flex items-center gap-2 mt-2">
                <input
                  type="color"
                  value={customColor}
                  onChange={handleCustomColorChange}
                  className="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                />
                <Input
                  value={customColor}
                  onChange={(e) => {
                    setCustomColor(e.target.value)
                    setBackground({ type: 'color', value: e.target.value })
                  }}
                  placeholder="#ffffff"
                  className="flex-1"
                />
              </div>
            </div>
          </TabsContent>

          {/* Gradients */}
          <TabsContent value="gradient" className="space-y-4">
            <div>
              <Label className="text-sm">預設漸層</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {GRADIENT_PRESETS.map((gradient, index) => (
                  <button
                    key={index}
                    className={cn(
                      "w-full h-12 rounded border-2 border-gray-300 hover:border-gray-400 transition-colors",
                      canvasData.background?.type === 'gradient' && 
                      canvasData.background?.value === gradient && 
                      "border-blue-500 ring-2 ring-blue-200"
                    )}
                    style={{ background: gradient }}
                    onClick={() => handleGradientChange(gradient)}
                  />
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Upload */}
          <TabsContent value="upload" className="space-y-4">
            <div>
              <Label className="text-sm">上傳圖片</Label>
              <div className="mt-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  選擇圖片檔案
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                支援 JPG, PNG 格式，建議尺寸 800x600 像素
              </p>
            </div>

            {canvasData.background?.type === 'image' && (
              <div>
                <Label className="text-sm">目前背景</Label>
                <div className="mt-2 border rounded overflow-hidden">
                  <img
                    src={canvasData.background.value}
                    alt="Background"
                    className="w-full h-24 object-cover"
                  />
                </div>
              </div>
            )}
          </TabsContent>

          {/* AI Generation */}
          <TabsContent value="ai" className="space-y-4">
            <div>
              <Label className="text-sm">AI 背景生成</Label>
              <div className="space-y-2 mt-2">
                <Input
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  placeholder="描述您想要的背景，例如：清晨的富士山、櫻花飛舞"
                  onKeyPress={(e) => e.key === 'Enter' && handleAiGenerate()}
                />
                <Button
                  onClick={handleAiGenerate}
                  disabled={!aiPrompt.trim() || isGenerating}
                  className="w-full flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  {isGenerating ? '生成中...' : '生成背景'}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                使用 AI 根據您的描述生成獨特的背景圖片
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Current Background Info */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            目前背景：
            {canvasData.background?.type === 'color' && '純色'}
            {canvasData.background?.type === 'gradient' && '漸層'}
            {canvasData.background?.type === 'image' && '圖片'}
            {!canvasData.background && '無'}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
