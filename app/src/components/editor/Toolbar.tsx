'use client'

import { useState } from 'react'
import { useEditor } from '@/hooks/useEditor'
import { Button } from '@/components/ui/button'
import { SaveTemplateDialog } from '@/components/template/SaveTemplateDialog'
import { ExportDialog } from '@/components/export/ExportDialog'
import {
  Type,
  Image,
  Palette,
  Undo,
  Redo,
  Trash2,
  Download,
  Save,
  BookOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ToolbarProps {
  onSave?: () => void
  onExport?: () => void
  onOpenTemplateLibrary?: () => void
}

export function Toolbar({ onSave, onExport, onOpenTemplateLibrary }: ToolbarProps) {
  const {
    tool,
    setTool,
    selectedObjectId,
    deleteLayer,
    undo,
    redo,
    clearCanvas,
    history,
    historyIndex
  } = useEditor()

  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)

  const tools = [
    { id: 'text' as const, icon: Type, label: '文字' },
    { id: 'object' as const, icon: Image, label: '物件' },
    { id: 'background' as const, icon: Palette, label: '背景' },
  ]

  const canUndo = historyIndex > 0
  const canRedo = historyIndex < history.length - 1

  return (
    <div className="flex items-center gap-2 p-4 bg-white border-b border-gray-200">
      {/* Tool Selection */}
      <div className="flex items-center gap-1 mr-4">
        {tools.map((toolItem) => {
          const Icon = toolItem.icon
          return (
            <Button
              key={toolItem.id}
              variant={tool === toolItem.id ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setTool(toolItem.id)}
              className={cn(
                'flex items-center gap-2',
                tool === toolItem.id && 'bg-blue-100 text-blue-700'
              )}
            >
              <Icon className="h-4 w-4" />
              <span className="hidden sm:inline">{toolItem.label}</span>
            </Button>
          )
        })}
      </div>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 mr-4" />

      {/* History Controls */}
      <div className="flex items-center gap-1 mr-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={undo}
          disabled={!canUndo}
          title="復原 (Ctrl+Z)"
        >
          <Undo className="h-4 w-4" />
          <span className="hidden sm:inline ml-2">復原</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={redo}
          disabled={!canRedo}
          title="重做 (Ctrl+Y)"
        >
          <Redo className="h-4 w-4" />
          <span className="hidden sm:inline ml-2">重做</span>
        </Button>
      </div>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 mr-4" />

      {/* Object Controls */}
      {selectedObjectId && (
        <>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteLayer(selectedObjectId)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
            title="刪除選中物件"
          >
            <Trash2 className="h-4 w-4" />
            <span className="hidden sm:inline ml-2">刪除</span>
          </Button>
          
          {/* Divider */}
          <div className="w-px h-6 bg-gray-300 mr-4" />
        </>
      )}

      {/* Canvas Controls */}
      <div className="flex items-center gap-1 mr-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={clearCanvas}
          className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
          title="清空畫布"
        >
          <Trash2 className="h-4 w-4" />
          <span className="hidden sm:inline ml-2">清空</span>
        </Button>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {onOpenTemplateLibrary && (
          <Button
            variant="outline"
            size="sm"
            onClick={onOpenTemplateLibrary}
            className="flex items-center gap-2"
          >
            <BookOpen className="h-4 w-4" />
            <span className="hidden sm:inline">樣板庫</span>
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => setSaveDialogOpen(true)}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          <span className="hidden sm:inline">存為樣板</span>
        </Button>

        {onSave && (
          <Button
            variant="outline"
            size="sm"
            onClick={onSave}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span className="hidden sm:inline">儲存</span>
          </Button>
        )}

        <Button
          size="sm"
          onClick={() => setExportDialogOpen(true)}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          <span className="hidden sm:inline">匯出</span>
        </Button>

        {onExport && (
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">快速匯出</span>
          </Button>
        )}
      </div>

      <SaveTemplateDialog
        open={saveDialogOpen}
        onOpenChange={setSaveDialogOpen}
        onSaved={(templateId) => {
          console.log('Template saved:', templateId)
        }}
      />

      <ExportDialog
        open={exportDialogOpen}
        onOpenChange={setExportDialogOpen}
      />
    </div>
  )
}
