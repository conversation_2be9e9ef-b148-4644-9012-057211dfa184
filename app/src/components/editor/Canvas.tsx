'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Stage, Layer, Rect, Image as KonvaImage, Text, Transformer } from 'react-konva'
import { useEditor } from '@/hooks/useEditor'
import { ObjectLayer, TextLayer } from '@/types'
import Konva from 'konva'

interface CanvasProps {
  width?: number
  height?: number
}

export function Canvas({ width = 800, height = 600 }: CanvasProps) {
  const {
    canvasData,
    selectedObjectId,
    selectObject,
    moveLayer,
    resizeLayer,
    rotateLayer,
    tool
  } = useEditor()
  
  const stageRef = useRef<Konva.Stage>(null)
  const transformerRef = useRef<Konva.Transformer>(null)
  const [images, setImages] = useState<{ [key: string]: HTMLImageElement }>({})

  // Load images for objects and background
  useEffect(() => {
    const loadImages = async () => {
      const imagePromises = []

      // Load object images
      canvasData.objects.forEach(obj => {
        if (!images[obj.id]) {
          imagePromises.push(
            new Promise<{ id: string; image: HTMLImageElement }>((resolve, reject) => {
              const img = new window.Image()
              img.crossOrigin = 'anonymous'
              img.onload = () => resolve({ id: obj.id, image: img })
              img.onerror = reject
              img.src = obj.src
            })
          )
        }
      })

      // Load background image
      if (canvasData.background?.type === 'image' && !images['background']) {
        imagePromises.push(
          new Promise<{ id: string; image: HTMLImageElement }>((resolve, reject) => {
            const img = new window.Image()
            img.crossOrigin = 'anonymous'
            img.onload = () => resolve({ id: 'background', image: img })
            img.onerror = reject
            img.src = canvasData.background!.value
          })
        )
      }

      if (imagePromises.length > 0) {
        try {
          const loadedImages = await Promise.all(imagePromises)
          const imageMap = loadedImages.reduce((acc, { id, image }) => {
            acc[id] = image
            return acc
          }, {} as { [key: string]: HTMLImageElement })

          setImages(prev => ({ ...prev, ...imageMap }))
        } catch (error) {
          console.error('Error loading images:', error)
        }
      }
    }

    loadImages()
  }, [canvasData.objects, canvasData.background, images])

  // Handle transformer
  useEffect(() => {
    if (transformerRef.current && selectedObjectId) {
      const stage = stageRef.current
      if (stage) {
        const selectedNode = stage.findOne(`#${selectedObjectId}`)
        if (selectedNode) {
          transformerRef.current.nodes([selectedNode])
          transformerRef.current.getLayer()?.batchDraw()
        }
      }
    } else if (transformerRef.current) {
      transformerRef.current.nodes([])
      transformerRef.current.getLayer()?.batchDraw()
    }
  }, [selectedObjectId])

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    if (e.target === e.target.getStage()) {
      selectObject(null)
      return
    }

    const clickedId = e.target.id()
    if (clickedId && tool === 'select') {
      selectObject(clickedId)
    }
  }

  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>, id: string) => {
    moveLayer(id, e.target.x(), e.target.y())
  }

  const handleTransformEnd = (e: Konva.KonvaEventObject<Event>, id: string) => {
    const node = e.target
    const scaleX = node.scaleX()
    const scaleY = node.scaleY()
    
    // Reset scale and apply to width/height
    node.scaleX(1)
    node.scaleY(1)
    
    const newWidth = Math.max(5, node.width() * scaleX)
    const newHeight = Math.max(5, node.height() * scaleY)
    
    resizeLayer(id, newWidth, newHeight)
    rotateLayer(id, node.rotation())
    moveLayer(id, node.x(), node.y())
  }

  const renderBackground = () => {
    if (!canvasData.background) return null

    if (canvasData.background.type === 'color') {
      return (
        <Rect
          width={width}
          height={height}
          fill={canvasData.background.value}
        />
      )
    }

    if (canvasData.background.type === 'gradient') {
      // Create a simple gradient effect using multiple rects
      return (
        <Rect
          width={width}
          height={height}
          fillLinearGradientStartPoint={{ x: 0, y: 0 }}
          fillLinearGradientEndPoint={{ x: width, y: height }}
          fillLinearGradientColorStops={[0, '#667eea', 1, '#764ba2']}
        />
      )
    }

    if (canvasData.background.type === 'image' && images['background']) {
      return (
        <KonvaImage
          image={images['background']}
          width={width}
          height={height}
          x={0}
          y={0}
        />
      )
    }

    return null
  }

  const renderObjects = () => {
    return canvasData.objects
      .sort((a, b) => a.zIndex - b.zIndex)
      .map((obj: ObjectLayer) => {
        const image = images[obj.id]
        if (!image) return null

        return (
          <KonvaImage
            key={obj.id}
            id={obj.id}
            image={image}
            x={obj.x}
            y={obj.y}
            width={obj.width}
            height={obj.height}
            rotation={obj.rotation}
            opacity={obj.opacity}
            scaleX={obj.flipX ? -1 : 1}
            scaleY={obj.flipY ? -1 : 1}
            draggable={tool === 'select'}
            onDragEnd={(e) => handleDragEnd(e, obj.id)}
            onTransformEnd={(e) => handleTransformEnd(e, obj.id)}
            onClick={() => tool === 'select' && selectObject(obj.id)}
          />
        )
      })
  }

  const renderTexts = () => {
    return canvasData.texts
      .sort((a, b) => a.zIndex - b.zIndex)
      .map((text: TextLayer) => (
        <React.Fragment key={text.id}>
          <Text
            id={text.id}
            text={text.content}
            x={text.x}
            y={text.y}
            width={text.width}
            height={text.height}
            fontSize={text.fontSize}
            fontFamily={text.fontFamily}
            fontStyle={text.fontWeight}
            fill={text.color}
            align={text.textAlign}
            rotation={text.rotation}
            opacity={text.opacity}
            stroke={text.stroke}
            strokeWidth={text.strokeWidth}
            shadowColor={text.shadow?.color}
            shadowBlur={text.shadow?.blur}
            shadowOffsetX={text.shadow?.offsetX}
            shadowOffsetY={text.shadow?.offsetY}
            draggable={tool === 'select'}
            onDragEnd={(e) => handleDragEnd(e, text.id)}
            onTransformEnd={(e) => handleTransformEnd(e, text.id)}
            onClick={() => {
              // 點擊文字時自動切換到選擇模式並選中文字
              if (tool !== 'select') {
                setTool('select')
              }
              selectObject(text.id)
            }}
          />
          {/* 為選中的文字添加虛線框 */}
          {selectedObjectId === text.id && (
            <Rect
              x={text.x - 5}
              y={text.y - 5}
              width={(text.width || text.content.length * text.fontSize * 0.6) + 10}
              height={(text.height || text.fontSize * 1.2) + 10}
              stroke="#007bff"
              strokeWidth={2}
              dash={[5, 5]}
              fill="transparent"
              listening={false}
            />
          )}
        </React.Fragment>
      ))
  }

  return (
    <div className="border border-gray-300 bg-white">
      <Stage
        ref={stageRef}
        width={width}
        height={height}
        onClick={handleStageClick}
        onTap={handleStageClick}
      >
        <Layer>
          {renderBackground()}
          {renderObjects()}
          {renderTexts()}
        </Layer>
        <Layer>
          <Transformer
            ref={transformerRef}
            boundBoxFunc={(oldBox, newBox) => {
              // Limit resize
              if (newBox.width < 5 || newBox.height < 5) {
                return oldBox
              }
              return newBox
            }}
          />
        </Layer>
      </Stage>
    </div>
  )
}
