'use client'

import { EditorProvider } from '@/hooks/useEditor'
import { Canvas } from './Canvas'
import { Toolbar } from './Toolbar'
import { LayerPanel } from './LayerPanel'
import { StepGuide } from './StepGuide'

interface EditorProps {
  onSave?: () => void
  onExport?: () => void
}

export function Editor({ onSave, onExport }: EditorProps) {
  return (
    <EditorProvider>
      <div className="h-screen flex flex-col bg-gray-50">
        {/* Toolbar */}
        <Toolbar onSave={onSave} onExport={onExport} />

        {/* Main Editor Area - 2 Column Layout */}
        <div className="flex-1 flex">
          {/* Left Panel - Steps Guide */}
          <div className="w-96 border-r border-gray-200 bg-white overflow-y-auto">
            <StepGuide />
          </div>

          {/* Right Panel - Canvas and Layer Panel */}
          <div className="flex-1 flex flex-col">
            {/* Canvas Area - Fixed at top, with shadow for better separation */}
            <div className="bg-white border-b border-gray-200 p-4 flex justify-center shadow-sm sticky top-0 z-10">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Canvas />
              </div>
            </div>

            {/* Layer Panel - Below canvas, scrollable */}
            <div className="flex-1 bg-white p-4 overflow-y-auto">
              <div className="max-w-md mx-auto">
                <LayerPanel />
              </div>
            </div>
          </div>
        </div>
      </div>
    </EditorProvider>
  )
}
