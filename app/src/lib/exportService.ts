import Konva from 'konva'
import { CanvasData } from '@/types'

export interface ExportOptions {
  format: 'png' | 'jpeg'
  quality?: number // 0-1, only for JPEG
  width?: number
  height?: number
  pixelRatio?: number
}

export class ExportService {
  // 匯出畫布為圖片
  static async exportCanvas(
    canvasData: CanvasData,
    options: ExportOptions = { format: 'png' }
  ): Promise<string> {
    const {
      format = 'png',
      quality = 0.9,
      width = canvasData.width,
      height = canvasData.height,
      pixelRatio = 2
    } = options

    // 創建離屏 Konva Stage
    const stage = new Konva.Stage({
      container: document.createElement('div'),
      width,
      height
    })

    const layer = new Konva.Layer()
    stage.add(layer)

    try {
      // 渲染背景
      await this.renderBackground(layer, canvasData, width, height)

      // 渲染物件（按 zIndex 排序）
      const sortedObjects = [...canvasData.objects].sort((a, b) => a.zIndex - b.zIndex)
      for (const obj of sortedObjects) {
        await this.renderObject(layer, obj)
      }

      // 渲染文字（按 zIndex 排序）
      const sortedTexts = [...canvasData.texts].sort((a, b) => a.zIndex - b.zIndex)
      for (const text of sortedTexts) {
        this.renderText(layer, text)
      }

      // 匯出圖片
      const dataURL = stage.toDataURL({
        mimeType: format === 'png' ? 'image/png' : 'image/jpeg',
        quality: format === 'jpeg' ? quality : undefined,
        pixelRatio
      })

      return dataURL
    } finally {
      // 清理資源
      stage.destroy()
    }
  }

  // 渲染背景
  private static async renderBackground(
    layer: Konva.Layer,
    canvasData: CanvasData,
    width: number,
    height: number
  ): Promise<void> {
    if (!canvasData.background) return

    if (canvasData.background.type === 'color') {
      const rect = new Konva.Rect({
        x: 0,
        y: 0,
        width,
        height,
        fill: canvasData.background.value
      })
      layer.add(rect)
    } else if (canvasData.background.type === 'image') {
      const image = await this.loadImage(canvasData.background.value)
      const konvaImage = new Konva.Image({
        x: 0,
        y: 0,
        width,
        height,
        image
      })
      layer.add(konvaImage)
    } else if (canvasData.background.type === 'gradient') {
      // 簡單的漸層實現
      const rect = new Konva.Rect({
        x: 0,
        y: 0,
        width,
        height,
        fillLinearGradientStartPoint: { x: 0, y: 0 },
        fillLinearGradientEndPoint: { x: width, y: height },
        fillLinearGradientColorStops: [0, '#667eea', 1, '#764ba2']
      })
      layer.add(rect)
    }
  }

  // 渲染物件
  private static async renderObject(layer: Konva.Layer, obj: any): Promise<void> {
    const image = await this.loadImage(obj.src)
    const konvaImage = new Konva.Image({
      x: obj.x,
      y: obj.y,
      width: obj.width,
      height: obj.height,
      rotation: obj.rotation,
      opacity: obj.opacity,
      scaleX: obj.flipX ? -1 : 1,
      scaleY: obj.flipY ? -1 : 1,
      image
    })
    layer.add(konvaImage)
  }

  // 渲染文字
  private static renderText(layer: Konva.Layer, text: any): void {
    const konvaText = new Konva.Text({
      x: text.x,
      y: text.y,
      width: text.width,
      height: text.height,
      text: text.content,
      fontSize: text.fontSize,
      fontFamily: text.fontFamily,
      fontStyle: text.fontWeight,
      fill: text.color,
      align: text.textAlign,
      rotation: text.rotation,
      opacity: text.opacity,
      stroke: text.stroke,
      strokeWidth: text.strokeWidth,
      shadowColor: text.shadow?.color,
      shadowBlur: text.shadow?.blur,
      shadowOffsetX: text.shadow?.offsetX,
      shadowOffsetY: text.shadow?.offsetY
    })
    layer.add(konvaText)
  }

  // 載入圖片
  private static loadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })
  }

  // 下載圖片
  static downloadImage(dataURL: string, filename: string): void {
    const link = document.createElement('a')
    link.download = filename
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 複製到剪貼簿
  static async copyToClipboard(dataURL: string): Promise<void> {
    try {
      // 將 data URL 轉換為 Blob
      const response = await fetch(dataURL)
      const blob = await response.blob()
      
      // 複製到剪貼簿
      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ])
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      throw error
    }
  }

  // 分享到社群媒體
  static shareToSocial(platform: 'facebook' | 'twitter' | 'line', imageURL?: string, text?: string): void {
    const encodedText = encodeURIComponent(text || '來看看我用早安圖產生器製作的圖片！')
    const encodedURL = encodeURIComponent(window.location.origin)
    
    let shareURL = ''
    
    switch (platform) {
      case 'facebook':
        shareURL = `https://www.facebook.com/sharer/sharer.php?u=${encodedURL}&quote=${encodedText}`
        break
      case 'twitter':
        shareURL = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedURL}`
        break
      case 'line':
        shareURL = `https://social-plugins.line.me/lineit/share?url=${encodedURL}&text=${encodedText}`
        break
    }
    
    if (shareURL) {
      window.open(shareURL, '_blank', 'width=600,height=400')
    }
  }

  // 生成分享連結
  static generateShareLink(templateId?: string): string {
    const baseURL = window.location.origin
    return templateId ? `${baseURL}/template/${templateId}` : baseURL
  }

  // 預覽圖片（在新視窗中開啟）
  static previewImage(dataURL: string): void {
    const newWindow = window.open()
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>圖片預覽</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                background: #f0f0f0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
              }
              img {
                max-width: 100%;
                max-height: 100%;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                border-radius: 8px;
              }
            </style>
          </head>
          <body>
            <img src="${dataURL}" alt="Generated Image" />
          </body>
        </html>
      `)
    }
  }

  // 獲取圖片資訊
  static getImageInfo(dataURL: string): Promise<{
    width: number
    height: number
    size: number
    format: string
  }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        // 估算檔案大小（base64 編碼後的大小）
        const base64Length = dataURL.split(',')[1].length
        const sizeInBytes = (base64Length * 3) / 4
        
        // 獲取格式
        const format = dataURL.split(';')[0].split('/')[1]
        
        resolve({
          width: img.width,
          height: img.height,
          size: sizeInBytes,
          format
        })
      }
      img.onerror = reject
      img.src = dataURL
    })
  }
}
